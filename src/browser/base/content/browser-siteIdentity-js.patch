diff --git a/browser/base/content/browser-siteIdentity.js b/browser/base/content/browser-siteIdentity.js
index a2a5f6ff71b9a6587e2a033aee39385abd319645..8111c6bcffdcfd51d03cc9dea4aac902d57c83d8 100644
--- a/browser/base/content/browser-siteIdentity.js
+++ b/browser/base/content/browser-siteIdentity.js
@@ -825,7 +825,7 @@ var gIdentityHandler = {
       // This is a secure internal Firefox page.
       this._identityBox.className = "chromeUI";
       let brandBundle = document.getElementById("bundle_brand");
-      icon_label = brandBundle.getString("brandShorterName");
+      icon_label = brandBundle.getString("brandShortName");
     } else if (this._pageExtensionPolicy) {
       // This is a WebExtension page.
       this._identityBox.className = "extensionPage";
@@ -1143,6 +1143,12 @@ var gIdentityHandler = {
       }
     }
 
+    if (connection == "chrome" || connection == "extension" || connection == "file") {
+      document.getElementById("identity-popup-mainView-panel-header").setAttribute("hidden", "true");
+    } else {
+      document.getElementById("identity-popup-mainView-panel-header").removeAttribute("hidden");
+    }
+
     // Push the appropriate strings out to the UI.
     document.l10n.setAttributes(
       this._identityPopupMainViewHeaderLabel,
