diff --git a/browser/base/content/nsContextMenu.sys.mjs b/browser/base/content/nsContextMenu.sys.mjs
index b12427b3c23447ab26499120f8395c716dae9e82..934f5c1f1b38d035cd7d3a5253a8d3cecad385f8 100644
--- a/browser/base/content/nsContextMenu.sys.mjs
+++ b/browser/base/content/nsContextMenu.sys.mjs
@@ -1054,6 +1054,8 @@ export class nsContextMenu {
         !this.isSecureAboutPage()
     );
 
+    this.showItem("context-zenSplitLink", this.onLink && !this.onMailtoLink && !this.onTelLink);
+
     let canNotStrip =
       lazy.STRIP_ON_SHARE_CAN_DISABLE && !this.#canStripParams();
 
