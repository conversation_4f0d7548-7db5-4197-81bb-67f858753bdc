diff --git a/browser/base/content/browser.xhtml b/browser/base/content/browser.xhtml
index 665b048cf149b9a6d0ccc43f8d9199f077b8ada2..d031a99ae5c9c6e69cfd831c7d271d8a8d097dc8 100644
--- a/browser/base/content/browser.xhtml
+++ b/browser/base/content/browser.xhtml
@@ -26,6 +26,7 @@
         sizemode="normal"
         retargetdocumentfocus="urlbar-input"
         scrolling="false"
+        zen-before-loaded="true"
         persist="screenX screenY width height sizemode"
         data-l10n-sync="true">
 <head>
@@ -103,8 +104,10 @@
 
   <title data-l10n-id="browser-main-window-default-title"></title>
 
+#include zen-preloaded.inc.xhtml
   <script src="chrome://browser/content/global-scripts.js"></script>
   <script src="chrome://browser/content/browser-main.js"></script>
+#include zen-assets.inc.xhtml
 </head>
 <html:body xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">
 # All sets except for popupsets (commands, keys, and stringbundles)
@@ -126,9 +129,11 @@
     </vbox>
   </html:template>
 
+<hbox id="zen-main-app-wrapper" flex="1" persist="zen-compact-mode" class="browser-toolbox-background">
 #include navigator-toolbox.inc.xhtml
 
 #include browser-box.inc.xhtml
+</hbox>
 
   <html:template id="customizationPanel">
     <box id="customization-container" flex="1" hidden="true"><![CDATA[
