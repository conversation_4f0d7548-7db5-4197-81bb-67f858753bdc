diff --git a/browser/base/content/browser-places.js b/browser/base/content/browser-places.js
index 62b3665394a8256ebedf88a8f10bb6766977cf57..12ac643f913290b20fb7adba8f224f49bb82bf5c 100644
--- a/browser/base/content/browser-places.js
+++ b/browser/base/content/browser-places.js
@@ -252,6 +252,8 @@ var StarUI = {
       });
     }
 
+    this._setIconAndPreviewImage();
+
     let onPanelReady = fn => {
       let target = this.panel;
       if (target.parentNode) {
@@ -303,6 +305,21 @@ var StarUI = {
     }
   },
 
+  _setIconAndPreviewImage() {
+    let faviconImage = this._element("zenEditBookmarkPanelFavicon");
+    faviconImage.removeAttribute("iconloadingprincipal");
+    faviconImage.removeAttribute("src");
+
+    let tab = gBrowser.selectedTab;
+    if (tab.hasAttribute("image") && !tab.hasAttribute("busy")) {
+      faviconImage.setAttribute(
+        "iconloadingprincipal",
+        tab.getAttribute("iconloadingprincipal")
+      );
+      faviconImage.setAttribute("src", tab.getAttribute("image"));
+    }
+  },
+
   removeBookmarkButtonCommand: function SU_removeBookmarkButtonCommand() {
     this._removeBookmarksOnPopupHidden = true;
     this.panel.hidePopup();
