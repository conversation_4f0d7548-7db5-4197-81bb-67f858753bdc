# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

        content/browser/zenThemeModifier.js                                     (../../zen/common/zenThemeModifier.js)
        content/browser/ZenStartup.mjs                                          (../../zen/common/ZenStartup.mjs)
        content/browser/ZenPreloadedScripts.js                                  (../../zen/common/ZenPreloadedScripts.js)
        content/browser/zen-sets.js                                             (../../zen/common/zen-sets.js)
        content/browser/ZenUIManager.mjs                                        (../../zen/common/ZenUIManager.mjs)
        content/browser/zen-components/ZenActorsManager.mjs                     (../../zen/common/ZenActorsManager.mjs)
        content/browser/ZenCustomizableUI.sys.mjs                               (../../zen/common/ZenCustomizableUI.sys.mjs)
        content/browser/zen-components/ZenUIMigration.mjs                       (../../zen/common/ZenUIMigration.mjs)
        content/browser/zen-components/ZenCommonUtils.mjs                       (../../zen/common/ZenCommonUtils.mjs)
        content/browser/zen-components/ZenSessionStore.mjs                      (../../zen/common/ZenSessionStore.mjs)
        content/browser/zen-components/ZenEmojisData.min.mjs                    (../../zen/common/emojis/ZenEmojisData.min.mjs)
        content/browser/zen-components/ZenEmojiPicker.mjs                       (../../zen/common/emojis/ZenEmojiPicker.mjs)
        content/browser/zen-components/ZenHasPolyfill.mjs                       (../../zen/common/ZenHasPolyfill.mjs)

        content/browser/zen-styles/zen-theme.css                                (../../zen/common/styles/zen-theme.css)
        content/browser/zen-styles/zen-buttons.css                              (../../zen/common/styles/zen-buttons.css)
        content/browser/zen-styles/zen-browser-ui.css                           (../../zen/common/styles/zen-browser-ui.css)
        content/browser/zen-styles/zen-animations.css                           (../../zen/common/styles/zen-animations.css)
        content/browser/zen-styles/zen-panel-ui.css                             (../../zen/common/styles/zen-panel-ui.css)
        content/browser/zen-styles/zen-single-components.css                    (../../zen/common/styles/zen-single-components.css)
        content/browser/zen-styles/zen-sidebar.css                              (../../zen/common/styles/zen-sidebar.css)
        content/browser/zen-styles/zen-toolbar.css                              (../../zen/common/styles/zen-toolbar.css)
        content/browser/zen-styles/zen-browser-container.css                    (../../zen/common/styles/zen-browser-container.css)
        content/browser/zen-styles/zen-urlbar.css                               (../../zen/common/styles/zen-urlbar.css)
        content/browser/zen-styles/zen-popup.css                                (../../zen/common/styles/zen-popup.css)
        content/browser/zen-styles/zen-branding.css                             (../../zen/common/styles/zen-branding.css)

        content/browser/zen-styles/zen-panels/bookmarks.css                     (../../zen/common/styles/zen-panels/bookmarks.css)
        content/browser/zen-styles/zen-panels/print.css                         (../../zen/common/styles/zen-panels/print.css)
        content/browser/zen-styles/zen-panels/dialog.css                        (../../zen/common/styles/zen-panels/dialog.css)

        content/browser/zen-components/ZenCompactMode.mjs                       (../../zen/compact-mode/ZenCompactMode.mjs)
*       content/browser/zen-styles/zen-compact-mode.css                         (../../zen/compact-mode/zen-compact-mode.css)

        content/browser/zen-components/ZenViewSplitter.mjs                      (../../zen/split-view/ZenViewSplitter.mjs)
        content/browser/zen-styles/zen-decks.css                                (../../zen/split-view/zen-decks.css)

        content/browser/zen-components/ZenMods.mjs                              (../../zen/mods/ZenMods.mjs)

        content/browser/zen-components/ZenWorkspaceIcons.mjs                    (../../zen/workspaces/ZenWorkspaceIcons.mjs)
        content/browser/zen-components/ZenWorkspace.mjs                         (../../zen/workspaces/ZenWorkspace.mjs)
        content/browser/zen-components/ZenWorkspaces.mjs                        (../../zen/workspaces/ZenWorkspaces.mjs)
        content/browser/zen-components/ZenWorkspaceCreation.mjs                 (../../zen/workspaces/ZenWorkspaceCreation.mjs)
        content/browser/zen-components/ZenWorkspacesStorage.mjs                 (../../zen/workspaces/ZenWorkspacesStorage.mjs)
        content/browser/zen-components/ZenWorkspacesSync.mjs                    (../../zen/workspaces/ZenWorkspacesSync.mjs)
        content/browser/zen-components/ZenGradientGenerator.mjs                 (../../zen/workspaces/ZenGradientGenerator.mjs)
*       content/browser/zen-styles/zen-workspaces.css                           (../../zen/workspaces/zen-workspaces.css)
        content/browser/zen-styles/zen-gradient-generator.css                   (../../zen/workspaces/zen-gradient-generator.css)

        content/browser/zen-components/ZenKeyboardShortcuts.mjs                 (../../zen/kbs/ZenKeyboardShortcuts.mjs)

        content/browser/zen-components/ZenPinnedTabsStorage.mjs                 (../../zen/tabs/ZenPinnedTabsStorage.mjs)
        content/browser/zen-components/ZenPinnedTabManager.mjs                  (../../zen/tabs/ZenPinnedTabManager.mjs)
*       content/browser/zen-styles/zen-tabs.css                                 (../../zen/tabs/zen-tabs.css)
        content/browser/zen-styles/zen-tabs/vertical-tabs.css                   (../../zen/tabs/zen-tabs/vertical-tabs.css)

        content/browser/zen-components/ZenGlanceManager.mjs                     (../../zen/glance/ZenGlanceManager.mjs)
        content/browser/zen-styles/zen-glance.css                               (../../zen/glance/zen-glance.css)

        content/browser/zen-components/ZenFolder.mjs                            (../../zen/folders/ZenFolder.mjs)
        content/browser/zen-components/ZenFolders.mjs                           (../../zen/folders/ZenFolders.mjs)
        content/browser/zen-styles/zen-folders.css                              (../../zen/folders/zen-folders.css)

        content/browser/zen-components/ZenWelcome.mjs                           (../../zen/welcome/ZenWelcome.mjs)
        content/browser/zen-styles/zen-welcome.css                              (../../zen/welcome/zen-welcome.css)

        content/browser/zen-components/ZenMediaController.mjs                   (../../zen/media/ZenMediaController.mjs)
        content/browser/zen-styles/zen-media-controls.css                       (../../zen/media/zen-media-controls.css)

        content/browser/zen-components/ZenDownloadAnimation.mjs                 (../../zen/downloads/ZenDownloadAnimation.mjs)
        content/browser/zen-styles/zen-download-arc-animation.css               (../../zen/downloads/zen-download-arc-animation.css)
        content/browser/zen-styles/zen-download-box-animation.css               (../../zen/downloads/zen-download-box-animation.css)


        # Images
        content/browser/zen-images/brand-header.svg                             (../../zen/images/brand-header.svg)
        content/browser/zen-images/layouts/collapsed.png                        (../../zen/images/layouts/collapsed.png)
        content/browser/zen-images/layouts/multiple-toolbar.png                 (../../zen/images/layouts/multiple-toolbar.png)
        content/browser/zen-images/layouts/single-toolbar.png                   (../../zen/images/layouts/single-toolbar.png)
        content/browser/zen-images/grain-bg.png                                 (../../zen/images/grain-bg.png)
        content/browser/zen-images/note-indicator.svg                           (../../zen/images/note-indicator.svg)

        content/browser/zen-images/downloads/download.svg                       (../../zen/images/downloads/download.svg)
        content/browser/zen-images/downloads/archive.svg                        (../../zen/images/downloads/archive.svg)

        # Fonts
        content/browser/zen-fonts/JunicodeVF-Italic.woff2                       (../../zen/fonts/JunicodeVF-Italic.woff2)
        content/browser/zen-fonts/JunicodeVF-Roman.woff2                        (../../zen/fonts/JunicodeVF-Roman.woff2)

        # JS Vendor
        content/browser/zen-vendor/tsparticles.confetti.bundle.min.js           (../../zen/vendor/tsparticles.confetti.bundle.min.js)
        content/browser/zen-vendor/motion.min.mjs                               (../../zen/vendor/motion.min.mjs)

        # FavIcons for startup
        content/browser/zen-images/favicons/calendar.ico                        (../../zen/images/favicons/calendar.ico)
        content/browser/zen-images/favicons/discord.ico                         (../../zen/images/favicons/discord.ico)
        content/browser/zen-images/favicons/figma.ico                           (../../zen/images/favicons/figma.ico)
        content/browser/zen-images/favicons/github.ico                          (../../zen/images/favicons/github.ico)
        content/browser/zen-images/favicons/notion.ico                          (../../zen/images/favicons/notion.ico)
        content/browser/zen-images/favicons/obsidian.ico                        (../../zen/images/favicons/obsidian.ico)
        content/browser/zen-images/favicons/slack.ico                           (../../zen/images/favicons/slack.ico)
        content/browser/zen-images/favicons/reddit.ico                          (../../zen/images/favicons/reddit.ico)
        content/browser/zen-images/favicons/x.ico                               (../../zen/images/favicons/x.ico)
        content/browser/zen-images/favicons/trello.ico                          (../../zen/images/favicons/trello.ico)
