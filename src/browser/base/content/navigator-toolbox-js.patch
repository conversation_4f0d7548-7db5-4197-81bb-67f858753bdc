diff --git a/browser/base/content/navigator-toolbox.js b/browser/base/content/navigator-toolbox.js
index 31d0ee325847834e7c58c1079225adf43d738bcd..4d2c69d4dd7e313bdb085530b3289dc9d93530a5 100644
--- a/browser/base/content/navigator-toolbox.js
+++ b/browser/base/content/navigator-toolbox.js
@@ -8,7 +8,7 @@
 document.addEventListener(
   "DOMContentLoaded",
   () => {
-    const navigatorToolbox = document.getElementById("navigator-toolbox");
+    const navigatorToolbox = document.getElementById("browser");
     const widgetOverflow = document.getElementById("widget-overflow");
 
     function onPopupShowing(event) {
@@ -187,6 +187,7 @@ document.addEventListener(
         #reload-button ,
         #urlbar-go-button,
         #reader-mode-button,
+        #zen-tabs-wrapper,
         #picture-in-picture-button,
         #urlbar-zoom-button,
         #star-button-box,
@@ -208,6 +209,7 @@ document.addEventListener(
         case "vertical-tabs-newtab-button":
         case "tabs-newtab-button":
         case "new-tab-button":
+        case "zen-tabs-wrapper":
           gBrowser.handleNewTabMiddleClick(element, event);
           break;
 
