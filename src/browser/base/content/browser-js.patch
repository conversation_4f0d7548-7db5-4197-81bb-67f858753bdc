diff --git a/browser/base/content/browser.js b/browser/base/content/browser.js
index 05f6aec3dac31231e15cb316f6e06d66ee87bea9..3bbbe8553777056798ea9ddce1744a8e48848a37 100644
--- a/browser/base/content/browser.js
+++ b/browser/base/content/browser.js
@@ -29,6 +29,7 @@ ChromeUtils.defineESModuleGetters(this, {
   ContextualIdentityService:
     "resource://gre/modules/ContextualIdentityService.sys.mjs",
   CustomizableUI: "resource:///modules/CustomizableUI.sys.mjs",
+  ZenCustomizableUI: "chrome://browser/content/ZenCustomizableUI.sys.mjs",
   DevToolsSocketStatus:
     "resource://devtools/shared/security/DevToolsSocketStatus.sys.mjs",
   DownloadUtils: "resource://gre/modules/DownloadUtils.sys.mjs",
@@ -2287,6 +2288,8 @@ var XULBrowserWindow = {
     AboutReaderParent.updateReaderButton(gBrowser.selectedBrowser);
     TranslationsParent.onLocationChange(gBrowser.selectedBrowser);
 
+    gZenPinnedTabManager.onLocationChange(gBrowser.selectedBrowser);
+
     PictureInPicture.updateUrlbarToggle(gBrowser.selectedBrowser);
 
     if (!gMultiProcessBrowser) {
@@ -4630,7 +4633,7 @@ function switchToTabHavingURI(
       ignoreQueryString || replaceQueryString,
       ignoreFragmentWhenComparing
     );
-    let browsers = aWindow.gBrowser.browsers;
+    let browsers = aWindow.gZenWorkspaces.allUsedBrowsers;
     for (let i = 0; i < browsers.length; i++) {
       let browser = browsers[i];
       let browserCompare = cleanURL(
@@ -4673,7 +4676,7 @@ function switchToTabHavingURI(
         }
 
         if (!doAdopt) {
-          aWindow.gBrowser.tabContainer.selectedIndex = i;
+          aWindow.gZenWorkspaces.switchIfNeeded(browser);
         }
 
         return true;
@@ -5489,6 +5492,9 @@ var ConfirmationHint = {
     MozXULElement.insertFTLIfNeeded("toolkit/branding/brandings.ftl");
     MozXULElement.insertFTLIfNeeded("browser/confirmationHints.ftl");
     document.l10n.setAttributes(this._message, messageId, options.l10nArgs);
+    if (window.gZenUIManager?.showToast) {
+      return window.gZenUIManager.showToast(messageId, options);
+    }
     if (options.descriptionId) {
       document.l10n.setAttributes(this._description, options.descriptionId);
       this._description.hidden = false;
