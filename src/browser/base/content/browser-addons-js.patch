diff --git a/browser/base/content/browser-addons.js b/browser/base/content/browser-addons.js
index 061edde0e05e0744ba1aa80df5f9699e264fe69b..cc2b2f04758ee9690429ea6c4aad959d858218e8 100644
--- a/browser/base/content/browser-addons.js
+++ b/browser/base/content/browser-addons.js
@@ -1057,7 +1057,7 @@ var gXPInstallObserver = {
       persistent: true,
       hideClose: true,
       popupOptions: {
-        position: "bottomright topright",
+        position: gZenUIManager.panelUIPosition,
       },
     };
 
@@ -1266,7 +1266,7 @@ var gXPInstallObserver = {
       hideClose: true,
       timeout: Date.now() + 30000,
       popupOptions: {
-        position: "bottomright topright",
+        position: gZenUIManager.panelUIPosition,
       },
     };
 
@@ -2571,7 +2571,7 @@ var gUnifiedExtensions = {
         this.recordButtonTelemetry(reason || "extensions_panel_showing");
         this.ensureButtonShownBeforeAttachingPanel(panel);
         PanelMultiView.openPopup(panel, this._button, {
-          position: "bottomright topright",
+          position: gZenUIManager.panelUIPosition,
           triggerEvent: aEvent,
         });
       }
@@ -2758,18 +2758,20 @@ var gUnifiedExtensions = {
       this._maybeMoveWidgetNodeBack(widgetId);
     }
 
-    this.pinToToolbar(widgetId, shouldPinToToolbar);
+    await this.pinToToolbar(widgetId, shouldPinToToolbar);
   },
 
-  pinToToolbar(widgetId, shouldPinToToolbar) {
+  async pinToToolbar(widgetId, shouldPinToToolbar) {
     let newArea = shouldPinToToolbar
       ? CustomizableUI.AREA_NAVBAR
       : CustomizableUI.AREA_ADDONS;
     let newPosition = shouldPinToToolbar ? undefined : 0;
+    await gZenVerticalTabsManager._preCustomize();
 
     CustomizableUI.addWidgetToArea(widgetId, newArea, newPosition);
     // addWidgetToArea() will trigger onWidgetAdded or onWidgetMoved as needed,
     // and our handlers will call updateAttention() as needed.
+    await gZenVerticalTabsManager._postCustomize();
   },
 
   async moveWidget(menu, direction) {
