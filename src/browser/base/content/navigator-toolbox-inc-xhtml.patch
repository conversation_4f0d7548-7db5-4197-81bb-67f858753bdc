diff --git a/browser/base/content/navigator-toolbox.inc.xhtml b/browser/base/content/navigator-toolbox.inc.xhtml
index 3484906e59f5da958a3f0c810c7b89cfce643d9a..7434caf4710af872d42ef4703ed8347cd8b07af2 100644
--- a/browser/base/content/navigator-toolbox.inc.xhtml
+++ b/browser/base/content/navigator-toolbox.inc.xhtml
@@ -2,7 +2,7 @@
 # License, v. 2.0. If a copy of the MPL was not distributed with this
 # file, You can obtain one at http://mozilla.org/MPL/2.0/.
 
-<toolbox id="navigator-toolbox" class="browser-toolbox-background">
+<toolbox id="navigator-toolbox" class="browser-toolbox-background" persist="width style">
   <script src="chrome://browser/content/navigator-toolbox.js" />
 
   <!-- Menu -->
@@ -17,9 +17,9 @@
 #include browser-menubar.inc
     </toolbaritem>
     <spacer flex="1" skipintoolbarset="true" style="order: 1000;"/>
-#include titlebar-items.inc.xhtml
   </toolbar>
 
+<hbox id="titlebar">
   <toolbar id="TabsToolbar"
            class="browser-toolbar browser-titlebar"
            fullscreentoolbar="true"
@@ -56,6 +56,8 @@
 # the current structure that we may want to revisit.
           <arrowscrollbox id="pinned-tabs-container" orient="horizontal"></arrowscrollbox>
           <splitter orient="vertical" id="vertical-pinned-tabs-splitter" resizebefore="sibling" resizeafter="none" hidden="true"/>
+<html:div id="zen-essentials" skipintoolbarset="true"></html:div>
+<html:div id="zen-tabs-wrapper">
           <hbox class="tab-drop-indicator" hidden="true"/>
           <arrowscrollbox id="tabbrowser-arrowscrollbox" orient="horizontal" flex="1" clicktoscroll="" scrolledtostart="" scrolledtoend="">
             <tab is="tabbrowser-tab" class="tabbrowser-tab" selected="true" visuallyselected="" fadein=""/>
@@ -75,6 +77,7 @@
                               tooltip="dynamic-shortcut-tooltip"
                               data-l10n-id="tabs-toolbar-new-tab"/>
           <html:span id="tabbrowser-tab-a11y-desc" hidden="true"/>
+</html:div>
         </tabs>
 
         <toolbarbutton id="new-tab-button"
@@ -100,9 +103,10 @@
 #include private-browsing-indicator.inc.xhtml
     <toolbarbutton class="content-analysis-indicator toolbarbutton-1 content-analysis-indicator-icon"/>
 
-#include titlebar-items.inc.xhtml
+#include zen-sidebar-icons.inc.xhtml
 
   </toolbar>
+</hbox>
 
   <toolbar id="nav-bar"
            class="browser-toolbar chromeclass-location"
