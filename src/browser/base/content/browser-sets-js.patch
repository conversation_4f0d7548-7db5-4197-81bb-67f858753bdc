diff --git a/browser/base/content/browser-sets.js b/browser/base/content/browser-sets.js
index 61aef2d420a78fb1910f556b71f6db75a16180ed..a3a1e70bedb760c165c338c28de6f2ee924df8de 100644
--- a/browser/base/content/browser-sets.js
+++ b/browser/base/content/browser-sets.js
@@ -250,7 +250,7 @@ document.addEventListener(
         }
       });
 
-    document.getElementById("mainKeyset").addEventListener("command", event => {
+    document.getElementById("zenKeyset").addEventListener("command", event => {
       const SIDEBAR_REVAMP_PREF = "sidebar.revamp";
       const SIDEBAR_REVAMP_ENABLED = Services.prefs.getBoolPref(
         SIDEBAR_REVAMP_PREF,
