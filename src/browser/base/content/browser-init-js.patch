diff --git a/browser/base/content/browser-init.js b/browser/base/content/browser-init.js
index bcbfab4a3781ff3c7349115751b3830976eec4bf..b1b774e0d335ed3c5ca565a45cd624c7ea2718af 100644
--- a/browser/base/content/browser-init.js
+++ b/browser/base/content/browser-init.js
@@ -186,6 +186,7 @@ var gBrowserInit = {
   },
 
   onLoad() {
+    Services.scriptloader.loadSubScript("chrome://browser/content/zenThemeModifier.js", window);
     gBrowser.addEventListener("DOMUpdateBlockedPopups", e =>
       PopupBlockerObserver.handleEvent(e)
     );
@@ -344,6 +345,7 @@ var gBrowserInit = {
 
     this._handleURIToLoad();
 
+    gZenWorkspaces.selectStartPage();
     Services.obs.addObserver(gIdentity<PERSON><PERSON><PERSON>, "perm-changed");
     Services.obs.addObserver(gRemoteControl, "devtools-socket");
     Services.obs.addObserver(gRemoteControl, "marionette-listening");
