diff --git a/browser/actors/WebRTCParent.sys.mjs b/browser/actors/WebRTCParent.sys.mjs
index 49ba7724801e699c60fac72d67b3b2d11b6c8c93..5a9f209b3e6b625c6346083ab7eace552a81a3b4 100644
--- a/browser/actors/WebRTCParent.sys.mjs
+++ b/browser/actors/WebRTCParent.sys.mjs
@@ -152,6 +152,7 @@ export class WebRTCParent extends JSWindowActorParent {
 
     let tabbrowser = browser.ownerGlobal.gBrowser;
     if (tabbrowser) {
+      browser.ownerGlobal.gZenMediaController.updateMediaSharing(state);
       tabbrowser.updateBrowserSharing(browser, {
         webRTC: state,
       });
