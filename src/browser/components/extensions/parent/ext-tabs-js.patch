diff --git a/browser/components/extensions/parent/ext-tabs.js b/browser/components/extensions/parent/ext-tabs.js
index a53a12f91817a9e3d1773480928e858bc3182c77..10f6559ffb128906be31de220c93320e9a6d40e7 100644
--- a/browser/components/extensions/parent/ext-tabs.js
+++ b/browser/components/extensions/parent/ext-tabs.js
@@ -494,6 +494,7 @@ this.tabs = class extends ExtensionAPIPersistent {
         }
 
         let tab = tabManager.getWrapper(updatedTab);
+        if (!tab) return;
 
         let changeInfo = {};
         for (let prop of needed) {
@@ -848,6 +849,7 @@ this.tabs = class extends ExtensionAPIPersistent {
               });
             }
 
+            window.gZenCompactModeManager._nextTimeWillBeActive = active;
             let nativeTab = window.gBrowser.addTab(url, options);
 
             if (active) {
