diff --git a/browser/components/customizableui/CustomizeMode.sys.mjs b/browser/components/customizableui/CustomizeMode.sys.mjs
index bc601c9cd248637f304b8f3219862e8e6f6c7506..da28068906ddeb81337696da568608edb43223b7 100644
--- a/browser/components/customizableui/CustomizeMode.sys.mjs
+++ b/browser/components/customizableui/CustomizeMode.sys.mjs
@@ -502,7 +502,7 @@ export class CustomizeMode {
       this.#transitioning = true;
 
       let customizer = document.getElementById("customization-container");
-      let browser = document.getElementById("browser");
+      let browser = document.getElementById("tabbrowser-tabbox"); // ZEN: This fixes customizing mode!!! Dont remove it
       browser.hidden = true;
       customizer.hidden = false;
 
@@ -639,7 +639,7 @@ export class CustomizeMode {
     }
 
     let customizer = document.getElementById("customization-container");
-    let browser = document.getElementById("browser");
+    let browser = document.getElementById("tabbrowser-tabbox"); // ZEN: This fixes customizing mode!!! Dont remove it
     customizer.hidden = true;
     browser.hidden = false;
 
@@ -1175,6 +1175,7 @@ export class CustomizeMode {
     return (
       aNode.localName == "toolbarbutton" ||
       aNode.localName == "toolbaritem" ||
+      aNode.localName == "zen-workspace-icons" ||
       aNode.localName == "toolbarseparator" ||
       aNode.localName == "toolbarspring" ||
       aNode.localName == "toolbarspacer"
@@ -3127,6 +3128,20 @@ export class CustomizeMode {
         if (makeSpaceImmediately) {
           aDraggedOverItem.setAttribute("notransition", "true");
         }
+        if (aItem.parentElement.id === "TabsToolbar-customization-target")  {
+          // We change the border values so we can properly implement the native vertical tabs
+          // drag and drop behavior.
+          aItem.style.borderColor = "transparent";
+          if (aValue == "before") {
+            prop = "borderTopWidth";
+            otherProp = "borderBottomWidth";
+            aItem.style.borderTopStyle = "solid";
+          } else {
+            prop = "borderBottomWidth";
+            otherProp = "borderTopWidth";
+            aItem.style.borderBottomStyle = "solid";
+          }
+        }
         aDraggedOverItem.style[prop] = borderWidth + "px";
         aDraggedOverItem.style.removeProperty(otherProp);
         if (makeSpaceImmediately) {
