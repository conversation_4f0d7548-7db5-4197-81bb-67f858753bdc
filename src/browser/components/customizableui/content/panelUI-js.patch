diff --git a/browser/components/customizableui/content/panelUI.js b/browser/components/customizableui/content/panelUI.js
index b3c4898fa5aa7ba997d84a2b4fdce398978e1150..736d7aca6232e94a1de0afe2c292060c5c622565 100644
--- a/browser/components/customizableui/content/panelUI.js
+++ b/browser/components/customizableui/content/panelUI.js
@@ -517,8 +517,7 @@ const PanelUI = {
         tempPanel.setAttribute("animate", "false");
       }
       tempPanel.setAttribute("context", "");
-      document
-        .getElementById(CustomizableUI.AREA_NAVBAR)
+      aAnchor.closest("toolbar")
         .appendChild(tempPanel);
 
       let multiView = document.createXULElement("panelmultiview");
@@ -609,10 +608,12 @@ const PanelUI = {
     if (hasKids && !this.navbar.hasAttribute("nonemptyoverflow")) {
       this.navbar.setAttribute("nonemptyoverflow", "true");
       this.overflowPanel.setAttribute("hasfixeditems", "true");
+      window.document.getElementById("zen-sidebar-top-buttons").setAttribute("nonemptyoverflow", "true");
     } else if (!hasKids && this.navbar.hasAttribute("nonemptyoverflow")) {
       PanelMultiView.hidePopup(this.overflowPanel);
       this.overflowPanel.removeAttribute("hasfixeditems");
       this.navbar.removeAttribute("nonemptyoverflow");
+      window.document.getElementById("zen-sidebar-top-buttons").removeAttribute("nonemptyoverflow");
     }
   },
 
@@ -960,7 +961,7 @@ const PanelUI = {
         el.removeAttribute("data-lazy-l10n-id");
       });
 
-    this.notificationPanel.openPopup(anchor, "bottomright topright");
+    this.notificationPanel.openPopup(anchor, gZenUIManager.panelUIPosition);
   },
 
   _clearNotificationPanel() {
