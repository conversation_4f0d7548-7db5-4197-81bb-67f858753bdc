diff --git a/browser/components/customizableui/CustomizableUI.sys.mjs b/browser/components/customizableui/CustomizableUI.sys.mjs
index 8c9b11d0c1ce7ae016889950e92b3adda318235c..ba81498aa16ce844a8d189c047f596d1969f4fed 100644
--- a/browser/components/customizableui/CustomizableUI.sys.mjs
+++ b/browser/components/customizableui/CustomizableUI.sys.mjs
@@ -13,6 +13,7 @@ ChromeUtils.defineESModuleGetters(lazy, {
   AddonManagerPrivate: "resource://gre/modules/AddonManager.sys.mjs",
   BrowserUsageTelemetry: "resource:///modules/BrowserUsageTelemetry.sys.mjs",
   CustomizableWidgets: "resource:///modules/CustomizableWidgets.sys.mjs",
+  ZenCustomizableUI: "chrome://browser/content/ZenCustomizableUI.sys.mjs",
   HomePage: "resource:///modules/HomePage.sys.mjs",
   PanelMultiView: "resource:///modules/PanelMultiView.sys.mjs",
   PrivateBrowsingUtils: "resource://gre/modules/PrivateBrowsingUtils.sys.mjs",
@@ -331,19 +332,14 @@ var CustomizableUIInternal = {
       "back-button",
       "forward-button",
       "stop-reload-button",
-      Services.policies.isAllowed("removeHomeButtonByDefault")
-        ? null
-        : "home-button",
       "spring",
       "vertical-spacer",
       "urlbar-container",
       "spring",
-      "downloads-button",
       AppConstants.MOZ_DEV_EDITION ? "developer-button" : null,
-      "fxa-toolbar-menu-button",
       lazy.resetPBMToolbarButtonEnabled ? "reset-pbm-toolbar-button" : null,
     ].filter(name => name);
-
+    lazy.ZenCustomizableUI.startup(this);
     this.registerArea(
       CustomizableUI.AREA_NAVBAR,
       {
@@ -351,8 +347,6 @@ var CustomizableUIInternal = {
         overflowable: true,
         defaultPlacements: navbarPlacements,
         verticalTabsDefaultPlacements: [
-          "firefox-view-button",
-          "alltabs-button",
         ],
         defaultCollapsed: false,
       },
@@ -376,10 +370,7 @@ var CustomizableUIInternal = {
       {
         type: CustomizableUI.TYPE_TOOLBAR,
         defaultPlacements: [
-          "firefox-view-button",
           "tabbrowser-tabs",
-          "new-tab-button",
-          "alltabs-button",
         ],
         verticalTabsDefaultPlacements: [],
         defaultCollapsed: null,
@@ -461,6 +452,7 @@ var CustomizableUIInternal = {
       CustomizableUI.AREA_NAVBAR,
       CustomizableUI.AREA_BOOKMARKS,
       CustomizableUI.AREA_TABSTRIP,
+      "zen-sidebar-top-buttons",
     ]);
     if (AppConstants.platform != "macosx") {
       toolbars.add(CustomizableUI.AREA_MENUBAR);
@@ -1245,6 +1237,9 @@ var CustomizableUIInternal = {
         placements = gPlacements.get(area);
       }
 
+      // remove "zen-sidebar-top-buttons" from the placements
+      placements = placements.filter(p => p != "zen-sidebar-top-buttons");
+
       // For toolbars that need it, mark as dirty.
       let defaultPlacements = areaProperties.get("defaultPlacements");
       if (
@@ -1752,7 +1747,7 @@ var CustomizableUIInternal = {
         lazy.log.info(
           "Widget " + aWidgetId + " not found, unable to remove from " + aArea
         );
-        continue;
+     // continue;
       }
 
       this.notifyDOMChange(widgetNode, null, container, true, () => {
@@ -1762,7 +1757,7 @@ var CustomizableUIInternal = {
         // We also need to remove the panel context menu if it's there:
         this.ensureButtonContextMenu(widgetNode);
         if (gPalette.has(aWidgetId) || this.isSpecialWidget(aWidgetId)) {
-          container.removeChild(widgetNode);
+          widgetNode.remove();
         } else {
           window.gNavToolbox.palette.appendChild(widgetNode);
         }
@@ -1930,16 +1925,16 @@ var CustomizableUIInternal = {
         elem.setAttribute("skipintoolbarset", "true");
       }
     }
-
+    lazy.ZenCustomizableUI.init(aWindow);
     // Now register all the toolbars
     for (let area of CustomizableUI.areas) {
       let type = CustomizableUI.getAreaType(area);
-      if (type == CustomizableUI.TYPE_TOOLBAR) {
+      if (type == CustomizableUI.TYPE_TOOLBAR && !area.startsWith("zen-")) {
         let node = document.getElementById(area);
         this.registerToolbarNode(node);
       }
     }
-
+    lazy.ZenCustomizableUI.registerToolbarNodes(aWindow);
     // Handle initial state of vertical tabs.
     if (isVerticalTabs) {
       // Show the vertical tabs toolbar
@@ -2181,6 +2176,10 @@ var CustomizableUIInternal = {
    *   The identifier string of the area that aNode is being inserted into.
    */
   insertWidgetBefore(aNode, aNextNode, aContainer, aAreaId) {
+    if (aAreaId === CustomizableUI.AREA_NAVBAR && aNode.ownerGlobal.gZenVerticalTabsManager._hasSetSingleToolbar) {
+      aContainer = aNode.ownerDocument.getElementById("zen-sidebar-top-buttons-customization-target");
+      aAreaId = "zen-sidebar-top-buttons";
+    }
     this.notifyDOMChange(aNode, aNextNode, aContainer, false, () => {
       this.setLocationAttributes(aNode, aAreaId);
       aContainer.insertBefore(aNode, aNextNode);
@@ -3301,7 +3300,6 @@ var CustomizableUIInternal = {
     if (!this.isWidgetRemovable(aWidgetId)) {
       return;
     }
-
     let placements = gPlacements.get(oldPlacement.area);
     let position = placements.indexOf(aWidgetId);
     if (position != -1) {
@@ -4536,7 +4534,7 @@ var CustomizableUIInternal = {
    * For all registered areas, builds those areas to reflect the current
    * placement state of all widgets.
    */
-  _rebuildRegisteredAreas() {
+  _rebuildRegisteredAreas(zenDontRebuildCollapsed = false) {
     for (let [areaId, areaNodes] of gBuildAreas) {
       let placements = gPlacements.get(areaId);
       let isFirstChangedToolbar = true;
@@ -4547,7 +4545,7 @@ var CustomizableUIInternal = {
         if (area.get("type") == CustomizableUI.TYPE_TOOLBAR) {
           let defaultCollapsed = area.get("defaultCollapsed");
           let win = areaNode.ownerGlobal;
-          if (defaultCollapsed !== null) {
+          if (defaultCollapsed !== null && !zenDontRebuildCollapsed) {
             win.setToolbarVisibility(
               areaNode,
               typeof defaultCollapsed == "string"
@@ -5838,6 +5836,7 @@ export var CustomizableUI = {
   unregisterArea(aName, aDestroyPlacements) {
     CustomizableUIInternal.unregisterArea(aName, aDestroyPlacements);
   },
+  get zenInternalCU() { return CustomizableUIInternal; },
   /**
    * Add a widget to an area.
    * If the area to which you try to add is not known to CustomizableUI,
@@ -7885,11 +7884,11 @@ class OverflowableToolbar {
         parseFloat(style.paddingLeft) -
         parseFloat(style.paddingRight) -
         toolbarChildrenWidth;
-      targetWidth = getInlineSize(this.#target);
+      targetWidth = getInlineSize(this.#target) - ((win.gZenVerticalTabsManager._hasSetSingleToolbar && this.#target.id == 'zen-sidebar-top-buttons-customization-target') ? win.gZenVerticalTabsManager._topButtonsSeparatorElement.getBoundingClientRect().width : 0);
       targetChildrenWidth =
         this.#target == this.#toolbar
           ? toolbarChildrenWidth
-          : sumChildrenInlineSize(this.#target);
+          : sumChildrenInlineSize((win.gZenVerticalTabsManager._hasSetSingleToolbar && this.#target.id == 'nav-bar-customization-target') ? win.document.getElementById("zen-sidebar-top-buttons-customization-target") : this.#target, win.gZenVerticalTabsManager._topButtonsSeparatorElement);
     });
 
     lazy.log.debug(
@@ -7899,7 +7898,8 @@ class OverflowableToolbar {
     // If the target has min-width: 0, their children might actually overflow
     // it, so check for both cases explicitly.
     let targetContentWidth = Math.max(targetWidth, targetChildrenWidth);
-    let isOverflowing = Math.floor(targetContentWidth) > totalAvailWidth;
+    if (win.gZenVerticalTabsManager._hasSetSingleToolbar && this.#toolbar.id == 'nav-bar') return { isOverflowing: false, targetContentWidth, totalAvailWidth };
+    let isOverflowing = Math.floor(targetContentWidth) + (win.gZenVerticalTabsManager._hasSetSingleToolbar ? 0.1 : 0) > totalAvailWidth;
     return { isOverflowing, targetContentWidth, totalAvailWidth };
   }
 
@@ -7993,7 +7993,7 @@ class OverflowableToolbar {
         }
       }
       if (!inserted) {
-        this.#target.appendChild(child);
+        win.gZenVerticalTabsManager.appendCustomizableItem(this.#target, child, gPlacements.get(this.#toolbar.id));
       }
       child.removeAttribute("cui-anchorid");
       child.removeAttribute("overflowedItem");
@@ -8338,7 +8338,7 @@ class OverflowableToolbar {
         break;
       }
       case "mousedown": {
-        if (aEvent.button != 0) {
+        if (aEvent.button != 0 || this.#toolbar.id == "zen-sidebar-top-buttons") { // Fix https://github.com/zen-browser/desktop/issues/7718
           break;
         }
         if (aEvent.target == this.#defaultListButton) {
