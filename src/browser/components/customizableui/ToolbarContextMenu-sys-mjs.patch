diff --git a/browser/components/customizableui/ToolbarContextMenu.sys.mjs b/browser/components/customizableui/ToolbarContextMenu.sys.mjs
index 0924175cecdceade096a325b2d4cd2656da24524..3c1e4a179d13aac740069c23e9bf8e40376d5032 100644
--- a/browser/components/customizableui/ToolbarContextMenu.sys.mjs
+++ b/browser/components/customizableui/ToolbarContextMenu.sys.mjs
@@ -241,8 +241,8 @@ export var ToolbarContextMenu = {
     // Show/hide sidebar and vertical tabs menu items
     let sidebarRevampEnabled = Services.prefs.getBoolPref("sidebar.revamp");
     let showSidebarActions =
-      ["tabbrowser-tabs", "sidebar-button"].includes(toolbarItem?.id) ||
-      toolbarItem?.localName == "toolbarspring";
+      (["tabbrowser-tabs", "sidebar-button"].includes(toolbarItem?.id) ||
+      toolbarItem?.localName == "toolbarspring") && false;
     let toggleVerticalTabsItem = document.getElementById(
       "toolbar-context-toggle-vertical-tabs"
     );
