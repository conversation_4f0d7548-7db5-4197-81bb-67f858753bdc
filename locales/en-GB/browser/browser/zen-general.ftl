zen-panel-ui-current-profile-text = Current profile
unified-extensions-description = Extensions are used to bring more extra functionality into { -brand-short-name }.
tab-context-zen-reset-pinned-tab = 
    .label = Reset Pinned Tab
    .accesskey = R
tab-context-zen-add-essential = 
    .label = Add to Essentials ({ $num } / 12 slots filled)
    .accesskey = E
tab-context-zen-remove-essential = 
    .label = Remove from Essentials
    .accesskey = R
tab-context-zen-replace-pinned-url-with-current = 
    .label = Replace Pinned URL with Current
    .accesskey = C
zen-themes-corrupted = Your { -brand-short-name } mods file is corrupted. They have been reset to the default theme.
zen-shortcuts-corrupted = Your { -brand-short-name } shortcuts file is corrupted. They have been reset to the default shortcuts.
# note: Do not translate the "<br/>" tags in the following string
zen-new-urlbar-notification = The new URL bar has been enabled, removing the need for new tab pages. Try opening a new tab to see the new URL bar in action!<br/><br/>
zen-disable = Disable
pictureinpicture-minimize-btn = 
    .aria-label = Minimize
    .tooltip = Minimize
zen-panel-ui-gradient-generator-custom-color = Custom colour
zen-panel-ui-gradient-generator-saved-message = Successfully saved the gradient!
zen-copy-current-url-confirmation = The URL has been copied to the clipboard.
zen-general-cancel-label = 
    .label = Cancel
zen-general-confirm = 
    .label = Confirm
zen-pinned-tab-replaced = Pinned tab URL has been replaced with the current URL.
zen-tabs-renamed = Tab has been successfully renamed!
zen-background-tab-opened-toast = New background tab opened!
zen-workspace-renamed-toast = Workspace has been successfully renamed!
zen-library-sidebar-workspaces = 
    .label = Spaces
zen-library-sidebar-mods = 
    .label = Mods

# note: Do not translate the "<br/>" tags in the following string

zen-learn-more-text = Learn More
zen-close-label = Close
zen-singletoolbar-urlbar-placeholder-with-name = 
    .placeholder = Search...
zen-icons-picker-emoji = 
    .label = Emojis
zen-icons-picker-svg = 
    .label = Icons
