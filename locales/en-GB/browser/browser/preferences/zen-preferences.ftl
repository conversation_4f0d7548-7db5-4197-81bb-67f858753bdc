pane-zen-looks-title = Look and Feel
category-zen-looks = 
    .tooltiptext = { pane-zen-looks-title }
zen-warning-language = Changing the default language could make it easier for Websites to track you.
zen-vertical-tabs-layout-header = Browser Layout
zen-vertical-tabs-layout-description = Choose the layout that suits you best
zen-layout-single-toolbar = Single toolbar
zen-layout-multiple-toolbar = Multiple toolbars
zen-layout-collapsed-toolbar = Collapsed toolbar
sync-currently-syncing-workspaces = Other Workspaces
sync-engine-workspaces = 
    .label = Other Workspaces
    .tooltiptext = Sync your workspaces across devices
    .accesskey = W
zen-glance-title = Glance
zen-glance-header = General settings for glance
zen-glance-description = Get a quick overview of your links without opening them in a new tab
zen-glance-trigger-label = Trigger method
zen-glance-enabled = 
    .label = Enable Glance
zen-glance-trigger-ctrl-click = 
    .label = Ctrl + Click
zen-glance-trigger-alt-click = 
    .label = Alt + Click
zen-glance-trigger-shift-click = 
    .label = Shift + Click
zen-glance-trigger-meta-click = 
    .label = Meta (Command) + Click
zen-glance-trigger-mantain-click = 
    .label = Maintain click (Coming Soon!)
zen-look-and-feel-compact-view-header = Show in compact view
zen-look-and-feel-compact-view-description = Only show the toolbars you use!
zen-look-and-feel-compact-view-enabled = 
    .label = Enable { -brand-short-name }'s compact mode
zen-look-and-feel-compact-view-top-toolbar = 
    .label = Hide the top toolbar as well in compact mode
zen-look-and-feel-compact-toolbar-flash-popup = 
    .label = Briefly make the toolbar pop-up when switching or opening new tabs in compact mode
pane-zen-tabs-title = Tab Management
category-zen-workspaces = 
    .tooltiptext = { pane-zen-tabs-title }
pane-settings-workspaces-title = Workspaces
zen-tabs-unloader-enabled = 
    .label = Enable Tab Unload
zen-look-and-feel-compact-toolbar-themed = 
    .label = Use themed background for compact toolbar
zen-look-and-feel-compact-sidebar-themed = 
    .label = Use themed background for compact sidebar
zen-workspace-continue-where-left-off = 
    .label = Continue where you left off
pane-zen-pinned-tab-manager-title = Pinned Tabs
zen-pinned-tab-manager-header = General settings for pinned tabs
zen-pinned-tab-manager-description = Manage additional behaviour of pinned tabs
zen-pinned-tab-manager-restore-pinned-tabs-to-pinned-url = 
    .label = Restore pinned tabs to their originally pinned URL on startup
zen-pinned-tab-manager-container-specific-essentials-enabled = 
    .label = Enable container-specific essentials
zen-pinned-tab-manager-close-shortcut-behavior-label = Close Tab Shortcut Behaviour
zen-pinned-tab-manager-reset-unload-switch-close-shortcut-option = 
    .label = Reset URL, unload and switch to next tab
zen-pinned-tab-manager-unload-switch-close-shortcut-option = 
    .label = Unload and switch to next tab
zen-pinned-tab-manager-reset-switch-close-shortcut-option = 
    .label = Reset URL and switch to next tab
zen-pinned-tab-manager-switch-close-shortcut-option = 
    .label = Switch to next tab
zen-pinned-tab-manager-reset-close-shortcut-option = 
    .label = Reset URL
zen-pinned-tab-manager-close-close-shortcut-option = 
    .label = Close tab
pane-zen-workspaces-header = Workspaces
zen-settings-workspaces-header = General settings for workspaces
zen-settings-workspaces-description = With workspaces, you can have multiple browsing sessions at once!
zen-settings-workspaces-enabled = 
    .label = Enable Workspaces
zen-settings-workspaces-hide-default-container-indicator = 
    .label = Hide the default container indicator in the tab bar
zen-key-unsaved = Unsaved shortcut! Please save it by clicking the "Escape" key after retyping it.
zen-key-conflict = Conflict with another shortcut
pane-zen-theme-title = Theme Settings
zen-vertical-tabs-title = Sidebar and tabs layout
zen-vertical-tabs-header = Vertical Tabs
zen-vertical-tabs-description = Manage your tabs in a vertical layout
zen-vertical-tabs-show-expand-button = 
    .label = Show Expand Button
zen-vertical-tabs-newtab-on-tab-list = 
    .label = Show New Tab Button on Tab List
zen-vertical-tabs-newtab-top-button-up = 
    .label = Move the new tab button to the top
zen-vertical-tabs-expand-tabs-by-default = Expand Tabs by Default
zen-vertical-tabs-dont-expand-tabs-by-default = Don't Expand Tabs by Default
zen-vertical-tabs-expand-tabs-on-hover = Expand Tabs on Hover (Won't work on compact mode)
zen-vertical-tabs-expand-tabs-header = How to expand tabs
zen-vertical-tabs-expand-tabs-description = Choose how to expand tabs in the sidebar
zen-theme-marketplace-header = Zen Mods
zen-theme-disable-all-enabled = 
    .title = Disable all themes
zen-theme-disable-all-disabled = 
    .title = Enable all themes
zen-theme-marketplace-description = Find and install themes from the store.
zen-theme-marketplace-remove-button = 
    .label = Remove Theme
zen-theme-marketplace-check-for-updates-button = 
    .label = Check for Updates
zen-theme-marketplace-import-button = 
    .label = Import mods
zen-theme-marketplace-export-button = 
    .label = Export Mods
zen-theme-marketplace-import-success = Mods imported successfully
zen-theme-marketplace-import-failure = There was an error importing the mods
zen-theme-marketplace-export-success = Mods exported successfully
zen-theme-marketplace-export-failure = There was an error exporting the mods
zen-theme-marketplace-updates-success = Theme updated successfully
zen-theme-marketplace-updates-failure = Couldn't find any updates!
zen-theme-marketplace-toggle-enabled-button = 
    .title = Disable Theme
zen-theme-marketplace-toggle-disabled-button = 
    .title = Enable Theme
zen-theme-marketplace-remove-confirmation = Are you sure you want to remove this mod?
zen-theme-marketplace-close-modal = Close
zen-theme-marketplace-theme-header-title = 
    .title = CSS Selector: { $name }
zen-theme-marketplace-dropdown-default-label = 
    .label = None
zen-theme-marketplace-input-default-placeholder = 
    .placeholder = Type something...
pane-zen-marketplace-title = Zen Mods
zen-themes-auto-update = 
    .label = Automatically update installed mods on startup
zen-settings-workspaces-force-container-tabs-to-workspace = 
    .label = Switch to workspace where container is set as default when opening container tabs
zen-theme-marketplace-link = Visit Store
zen-dark-theme-styles-header = Dark Theme Styles
zen-dark-theme-styles-description = Customize the dark theme to your liking
zen-dark-theme-styles-amoled = Night Theme
zen-dark-theme-styles-default = Default Dark Theme
zen-dark-theme-styles-colorful = Colourful Dark Theme
zen-compact-mode-styles-left = Hide Tab bar
zen-compact-mode-styles-top = Hide Top bar
zen-compact-mode-styles-both = Hide Both
zen-urlbar-title = Zen URL Bar
zen-urlbar-header = General settings for the URL bar
zen-urlbar-description = Customize the URL bar to your liking
zen-urlbar-behavior-label = Behaviour
zen-urlbar-behavior-normal = 
    .label = Normal
zen-urlbar-behavior-floating-on-type = 
    .label = Floating only when typing
zen-urlbar-behavior-float = 
    .label = Always floating
pane-zen-CKS-title = Keyboard Shortcuts
category-zen-CKS = 
    .tooltiptext = { pane-zen-CKS-title }
pane-settings-CKS-title = { -brand-short-name } Keyboard Shortcuts
zen-settings-CKS-header = Customize your keyboard shortcuts
zen-settings-CKS-description = Change the default keyboard shortcuts to your liking and improve your browsing experience
zen-settings-CKS-disable-firefox = 
    .label = Disable { -brand-short-name }'s default keyboard shortcuts
zen-settings-CKS-duplicate-shortcut = 
    .label = Duplicate Shortcut
zen-settings-CKS-reset-shortcuts = 
    .label = Reset to Default
zenCKSOption-group-other = Other
zenCKSOption-group-windowAndTabManagement = Window & Tab Management
zenCKSOption-group-navigation = Navigation
zenCKSOption-group-searchAndFind = Search & Find
zenCKSOption-group-pageOperations = Page Operations
zenCKSOption-group-historyAndBookmarks = History & Bookmarks
zenCKSOption-group-mediaAndDisplay = Media & Display
zenCKSOption-group-zen-compact-mode = Compact Mode
zenCKSOption-group-zen-workspace = Other Workspaces
zenCKSOption-group-zen-other = Other Zen Features
zenCKSOption-group-zen-split-view = Zen-split-view-modifier-header
zenCKSOption-group-devTools = Developer Tools
zen-key-quick-restart = Quick Restart
zen-window-new-shortcut = New Window
zen-tab-new-shortcut = New Tab
zen-key-redo = Redo
zen-restore-last-closed-tab-shortcut = Restore Last Closed Tab
zen-location-open-shortcut = Open Location
zen-location-open-shortcut-alt = Open Location (Alt)
zen-key-undo-close-window = Undo Close Window
zen-text-action-undo-shortcut = Undo
zen-text-action-redo-shortcut = Redo
zen-text-action-cut-shortcut = Cut
zen-text-action-copy-shortcut = Copy
zen-text-action-copy-url-shortcut = Copy current URL
zen-text-action-copy-url-markdown-shortcut = Copy current URL as Markdown
zen-text-action-paste-shortcut = Paste
zen-text-action-select-all-shortcut = Select All
zen-text-action-delete-shortcut = Delete
zen-history-show-all-shortcut-mac = Show All History (Mac)
zen-full-screen-shortcut = Toggle Full Screen
zen-reader-mode-toggle-shortcut-windows = Toggle Reader Mode (Windows)
zen-picture-in-picture-toggle-shortcut-alt = Toggle Picture-in-Picture (Alt)
zen-picture-in-picture-toggle-shortcut-mac = Toggle Picture-in-Picture (Mac)
zen-picture-in-picture-toggle-shortcut-mac-alt = Toggle Picture-in-Picture (Mac Alt)
zen-page-source-shortcut-safari = View Page Source (Safari)
zen-nav-stop-shortcut = Stop Loading
zen-history-sidebar-shortcut = Show History Sidebar
zen-window-minimize-shortcut = Minimize Window
zen-help-shortcut = Open Help
zen-preferences-shortcut = Open Preferences
zen-hide-app-shortcut = Hide Application
zen-hide-other-apps-shortcut = Hide Other Applications
zen-search-focus-shortcut = Focus Search
zen-search-focus-shortcut-alt = Focus Search (Alt)
zen-downloads-shortcut = Open Downloads
zen-addons-shortcut = Open Add-ons
zen-file-open-shortcut = Open File
zen-save-page-shortcut = Save Page
zen-print-shortcut = Print Page
zen-close-shortcut-2 = Close Tab
zen-mute-toggle-shortcut = Toggle Mute
zen-key-delete = Delete Key
zen-key-go-back = Go Back
zen-key-go-forward = Go Forward
zen-nav-back-shortcut-alt = Navigate Back (Alt)
zen-nav-fwd-shortcut-alt = Navigate Forward (Alt)
zen-history-show-all-shortcut = Show All History
zen-key-enter-full-screen = Enter Full Screen
zen-key-exit-full-screen = Exit Full Screen
zen-ai-chatbot-sidebar-shortcut = Toggle AI Chatbot Sidebar
zen-key-inspector-mac = Toggle Inspector (Mac)
zen-toggle-sidebar-shortcut = Toggle Firefox Sidebar
zen-reader-mode-toggle-shortcut-other = Toggle Reader Mode
zen-picture-in-picture-toggle-shortcut = Toggle Picture-in-Picture
zen-nav-reload-shortcut-2 = Reload Page
zen-key-about-processes = About Processes
zen-page-source-shortcut = View Page Source
zen-page-info-shortcut = View Page Info
zen-find-shortcut = Find on Page
zen-search-find-again-shortcut = Find Again
zen-search-find-again-shortcut-prev = Find Previous
zen-search-find-again-shortcut-2 = Find Again (Alt)
zen-bookmark-this-page-shortcut = Bookmark This Page
zen-bookmark-show-library-shortcut = Show Bookmarks Library
zen-key-stop = Stop Loading
zen-full-zoom-reduce-shortcut = Zoom Out
zen-full-zoom-enlarge-shortcut = Zoom In
zen-full-zoom-reset-shortcut = Reset Zoom
zen-full-zoom-reset-shortcut-alt = Reset Zoom (Alt)
zen-full-zoom-enlarge-shortcut-alt = Zoom In (Alt)
zen-full-zoom-enlarge-shortcut-alt2 = Zoom In (Alt 2)
zen-bidi-switch-direction-shortcut = Switch Text Direction
zen-private-browsing-shortcut = Private Browsing
zen-screenshot-shortcut = Take Screenshot
zen-key-sanitize = Clear Browsing Data
zen-quit-app-shortcut = Quit Application
zen-key-wr-capture-cmd = WR Capture Command
zen-key-wr-toggle-capture-sequence-cmd = Toggle WR Capture Sequence
zen-nav-reload-shortcut = Reload Page
zen-nav-reload-shortcut-skip-cache = Reload Page (Skip Cache)
zen-close-shortcut = Close Window
zen-close-tab-shortcut = Close Tab
zen-compact-mode-shortcut-show-sidebar = Toggle Floating Sidebar
zen-compact-mode-shortcut-show-toolbar = Toggle Floating Toolbar
zen-compact-mode-shortcut-toggle = Toggle Compact Mode
zen-workspace-shortcut-switch-1 = Switch to Workspace 1
zen-workspace-shortcut-switch-2 = Switch to Workspace 2
zen-workspace-shortcut-switch-3 = Switch to Workspace 3
zen-workspace-shortcut-switch-4 = Switch to Workspace 4
zen-workspace-shortcut-switch-5 = Switch to Workspace 5
zen-workspace-shortcut-switch-6 = Switch to Workspace 6
zen-workspace-shortcut-switch-7 = Switch to Workspace 7
zen-workspace-shortcut-switch-8 = Switch to Workspace 8
zen-workspace-shortcut-switch-9 = Switch to Workspace 9
zen-workspace-shortcut-switch-10 = Switch to Workspace 10
zen-workspace-shortcut-forward = Forward Workspace
zen-workspace-shortcut-backward = Backward Workspace
zen-sidebar-shortcut-toggle = Toggle Sidebar's Width
zen-pinned-tab-shortcut-reset = Reset Pinned Tab to Pinned URL
zen-split-view-shortcut-grid = Toggle Split View Grid
zen-split-view-shortcut-vertical = Toggle Split View Vertical
zen-split-view-shortcut-horizontal = Toggle Split View Horizontal
zen-split-view-shortcut-unsplit = Close Split View
zen-key-select-tab-1 = Select tab #1
zen-key-select-tab-2 = Select tab #2
zen-key-select-tab-3 = Select tab #3
zen-key-select-tab-4 = Select tab #4
zen-key-select-tab-5 = Select tab #5
zen-key-select-tab-6 = Select tab #6
zen-key-select-tab-7 = Select tab #7
zen-key-select-tab-8 = Select tab #8
zen-key-select-tab-last = Select last tab
zen-key-show-all-tabs = Show all tabs
zen-key-goto-history = Go to history
zen-key-go-home = Go Home
zen-bookmark-show-sidebar-shortcut = Show Bookmarks Sidebar
zen-bookmark-show-toolbar-shortcut = Show Bookmarks Toolbar
zen-devtools-toggle-shortcut = Toggle DevTools
zen-devtools-toggle-browser-toolbox-shortcut = Toggle Browser Toolbox
zen-devtools-toggle-browser-console-shortcut = Toggle Browser Console
zen-devtools-toggle-responsive-design-mode-shortcut = Toggle Responsive Design Mode
zen-devtools-toggle-inspector-shortcut = Toggle Inspector
zen-devtools-toggle-web-console-shortcut = Toggle Web Console
zen-devtools-toggle-js-debugger-shortcut = Toggle JavaScript Debugger
zen-devtools-toggle-net-monitor-shortcut = Toggle Network Monitor
zen-devtools-toggle-style-editor-shortcut = Toggle Style Editor
zen-devtools-toggle-performance-shortcut = Toggle Performance
zen-devtools-toggle-storage-shortcut = Toggle Storage
zen-devtools-toggle-dom-shortcut = Toggle DOM
zen-devtools-toggle-accessibility-shortcut = Toggle Accessibility
