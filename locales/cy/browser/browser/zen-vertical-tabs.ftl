zen-toolbar-context-tabs-right = 
    .label = Tabs on the right
    .accesskey = R
zen-toolbar-context-compact-mode = 
    .label = Compact mode
    .accesskey = C
zen-toolbar-context-compact-mode-enable = 
    .label = Enable compact mode
    .accesskey = D
zen-toolbar-context-compact-mode-just-tabs = 
    .label = Hide sidebar
zen-toolbar-context-compact-mode-just-toolbar = 
    .label = Hide toolbar
zen-toolbar-context-compact-mode-hide-both = 
    .label = Hide both
    .accesskey = H
zen-toolbar-context-new-folder = 
    .label = New folder
    .accesskey = N
sidebar-zen-expand = 
    .label = Expand Sidebar
sidebar-zen-create-new = 
    .label = Create New...
tabbrowser-unload-tab-button = 
    .tooltiptext =
        { $tabCount ->
            [one] Unload and switch to tab
           *[other] Unload { $tabCount } tabs and switch to the first
        }
tabbrowser-reset-pin-button = 
    .tooltiptext =
        { $tabCount ->
            [one] Reset and pin tab
           *[other] Reset and pin { $tabCount } tabs
        }
tab-reset-pin-label = Back to pinned url
