pane-zen-looks-title = Erscheinungsbild
category-zen-looks = 
    .tooltiptext = { pane-zen-looks-title }
zen-warning-language = Das Ändern der Standardsprache könnte es Websites einfacher machen, <PERSON><PERSON> zu verfolgen.
zen-vertical-tabs-layout-header = Browser-Layout
zen-vertical-tabs-layout-description = Wählen Sie das Layout, das am besten zu Ihnen passt
zen-layout-single-toolbar = Einzelne Symbolleiste
zen-layout-multiple-toolbar = Mehrere Symbolleisten
zen-layout-collapsed-toolbar = Eingeklappte Symbolleiste
sync-currently-syncing-workspaces = Arbeitsbereiche
sync-engine-workspaces = 
    .label = Arbeitsbereiche
    .tooltiptext = Arbeitsbereiche zwischen Geräten synchronisieren
    .accesskey = W
zen-glance-title = Schnellansicht
zen-glance-header = Allgemeine Einstellungen für die Schnellansicht
zen-glance-description = Erhalten Sie einen schnellen Überblick über Ihre Links, ohne sie in einem neuen Tab zu öffnen
zen-glance-trigger-label = Auslösemethode
zen-glance-enabled = 
    .label = Schnellansicht aktivieren
zen-glance-trigger-ctrl-click = 
    .label = Strg + Klick
zen-glance-trigger-alt-click = 
    .label = Alt + Klick
zen-glance-trigger-shift-click = 
    .label = Umschalt + Klick
zen-glance-trigger-meta-click = 
    .label = Meta (Befehlstaste) + Klick
zen-glance-trigger-mantain-click = 
    .label = Klick halten (Kommt bald!)
zen-look-and-feel-compact-view-header = In der Kompaktansicht anzeigen
zen-look-and-feel-compact-view-description = Zeigen Sie nur die Symbolleisten an, die Sie verwenden!
zen-look-and-feel-compact-view-enabled = 
    .label = { -brand-short-name } Kompaktmodus aktivieren
zen-look-and-feel-compact-view-top-toolbar = 
    .label = Die obere Symbolleiste auch im Kompaktmodus ausblenden
zen-look-and-feel-compact-toolbar-flash-popup = 
    .label = Symbolleiste im Kompaktmodus beim Wechseln oder Öffnen neuer Tabs kurz einblenden
pane-zen-tabs-title = Tab-Verwaltung
category-zen-workspaces = 
    .tooltiptext = { pane-zen-tabs-title }
pane-settings-workspaces-title = Arbeitsbereiche
zen-tabs-unloader-enabled = 
    .label = Tab-Entlader aktivieren
zen-look-and-feel-compact-toolbar-themed = 
    .label = Themenhintergrund für kompakte Symbolleiste verwenden
zen-look-and-feel-compact-sidebar-themed = 
    .label = Themenhintergrund für kompakte Seitenleiste verwenden
zen-workspace-continue-where-left-off = 
    .label = Dort fortfahren, wo Sie aufgehört haben
pane-zen-pinned-tab-manager-title = Angepinnte Tabs
zen-pinned-tab-manager-header = Allgemeine Einstellungen für angepinnte Tabs
zen-pinned-tab-manager-description = Zusätzliches Verhalten von angepinnten Tabs verwalten
zen-pinned-tab-manager-restore-pinned-tabs-to-pinned-url = 
    .label = Angepinnte Tabs beim Start auf ihre ursprünglich angepinnte URL zurücksetzen
zen-pinned-tab-manager-container-specific-essentials-enabled = 
    .label = Container-spezifische Essentials aktivieren
zen-pinned-tab-manager-close-shortcut-behavior-label = Verhalten der Tastenkombination zum Schließen von Tabs
zen-pinned-tab-manager-reset-unload-switch-close-shortcut-option = 
    .label = URL zurücksetzen, entladen und zum nächsten Tab wechseln
zen-pinned-tab-manager-unload-switch-close-shortcut-option = 
    .label = Entladen und zum nächsten Tab wechseln
zen-pinned-tab-manager-reset-switch-close-shortcut-option = 
    .label = URL zurücksetzen und zum nächsten Tab wechseln
zen-pinned-tab-manager-switch-close-shortcut-option = 
    .label = Zum nächsten Tab wechseln
zen-pinned-tab-manager-reset-close-shortcut-option = 
    .label = URL zurücksetzen
zen-pinned-tab-manager-close-close-shortcut-option = 
    .label = Tab schließen
pane-zen-workspaces-header = Arbeitsbereiche
zen-settings-workspaces-header = Allgemeine Einstellungen für Arbeitsbereiche
zen-settings-workspaces-description = Mit Arbeitsbereichen können Sie mehrere Browser-Sitzungen gleichzeitig haben!
zen-settings-workspaces-enabled = 
    .label = Arbeitsbereiche aktivieren
zen-settings-workspaces-hide-default-container-indicator = 
    .label = Standard-Container-Anzeige in der Tab-Leiste ausblenden
zen-key-unsaved = Nicht gespeichertes Tastenkürzel! Bitte speichern Sie es, indem Sie nach der Neueingabe die "Escape"-Taste drücken.
zen-key-conflict = Konflikt mit einem anderen Tastenkürzel
pane-zen-theme-title = Design-Einstellungen
zen-vertical-tabs-title = Seitenleiste und Tab-Layout
zen-vertical-tabs-header = Vertikale Tabs
zen-vertical-tabs-description = Verwalten Sie Ihre Tabs in einem vertikalen Layout
zen-vertical-tabs-show-expand-button = 
    .label = Erweitern-Schaltfläche anzeigen
zen-vertical-tabs-newtab-on-tab-list = 
    .label = Schaltfläche "Neuer Tab" in der Tab-Liste anzeigen
zen-vertical-tabs-newtab-top-button-up = 
    .label = Schaltfläche "Neuer Tab" nach oben verschieben
zen-vertical-tabs-expand-tabs-by-default = Tabs standardmäßig erweitern
zen-vertical-tabs-dont-expand-tabs-by-default = Tabs standardmäßig nicht erweitern
zen-vertical-tabs-expand-tabs-on-hover = Tabs beim Drüberfahren erweitern (funktioniert nicht im Kompaktmodus)
zen-vertical-tabs-expand-tabs-header = Wie Tabs erweitert werden sollen
zen-vertical-tabs-expand-tabs-description = Wählen Sie, wie Tabs in der Seitenleiste erweitert werden sollen
zen-theme-marketplace-header = Zen Mods
zen-theme-disable-all-enabled = 
    .title = Alle Mods deaktivieren
zen-theme-disable-all-disabled = 
    .title = Alle Mods aktivieren
zen-theme-marketplace-description = Finden und installieren Sie Mods aus dem Store.
zen-theme-marketplace-remove-button = 
    .label = Mod entfernen
zen-theme-marketplace-check-for-updates-button = 
    .label = Auf Updates prüfen
zen-theme-marketplace-import-button = 
    .label = Mods importieren
zen-theme-marketplace-export-button = 
    .label = Mods exportieren
zen-theme-marketplace-import-success = Mods erfolgreich importiert
zen-theme-marketplace-import-failure = Beim Importieren der Mods ist ein Fehler aufgetreten
zen-theme-marketplace-export-success = Mods erfolgreich exportiert
zen-theme-marketplace-export-failure = Beim Exportieren der Mods ist ein Fehler aufgetreten
zen-theme-marketplace-updates-success = Mods wurden erfolgreich aktualisiert
zen-theme-marketplace-updates-failure = Es konnten keine Updates gefunden werden!
zen-theme-marketplace-toggle-enabled-button = 
    .title = Mod deaktivieren
zen-theme-marketplace-toggle-disabled-button = 
    .title = Mod aktivieren
zen-theme-marketplace-remove-confirmation = Sind Sie sicher, dass Sie diesen Mod entfernen möchten?
zen-theme-marketplace-close-modal = Schließen
zen-theme-marketplace-theme-header-title = 
    .title = CSS-Selektor: { $name }
zen-theme-marketplace-dropdown-default-label = 
    .label = Keine
zen-theme-marketplace-input-default-placeholder = 
    .placeholder = Etwas eingeben...
pane-zen-marketplace-title = Zen Mods
zen-themes-auto-update = 
    .label = Installierte Mods beim Start automatisch aktualisieren
zen-settings-workspaces-force-container-tabs-to-workspace = 
    .label = Zum Arbeitsbereich wechseln, in dem Container als Standard gesetzt ist, wenn Container-Tabs geöffnet werden
zen-theme-marketplace-link = Store besuchen
zen-dark-theme-styles-header = Dunkles Design - Stile
zen-dark-theme-styles-description = Passen Sie das dunkle Design nach Ihren Wünschen an
zen-dark-theme-styles-amoled = Nacht-Design
zen-dark-theme-styles-default = Standard dunkles Design
zen-dark-theme-styles-colorful = Farbenfrohes dunkles Design
zen-compact-mode-styles-left = Tab-Leiste ausblenden
zen-compact-mode-styles-top = Obere Leiste ausblenden
zen-compact-mode-styles-both = Beide ausblenden
zen-urlbar-title = Zen Adressleiste
zen-urlbar-header = Allgemeine Einstellungen für die Adressleiste
zen-urlbar-description = Passen Sie die Adressleiste nach Ihren Wünschen an
zen-urlbar-behavior-label = Verhalten
zen-urlbar-behavior-normal = 
    .label = Normal
zen-urlbar-behavior-floating-on-type = 
    .label = Schwebend nur beim Eingeben
zen-urlbar-behavior-float = 
    .label = Immer schwebend
pane-zen-CKS-title = Tastenkürzel
category-zen-CKS = 
    .tooltiptext = { pane-zen-CKS-title }
pane-settings-CKS-title = { -brand-short-name } Tastenkürzel
zen-settings-CKS-header = Passen Sie Ihre Tastenkürzel an
zen-settings-CKS-description = Ändern Sie die Standard-Tastenkürzel nach Ihren Wünschen und verbessern Sie Ihr Browser-Erlebnis
zen-settings-CKS-disable-firefox = 
    .label = { -brand-short-name } Standard-Tastenkürzel deaktivieren
zen-settings-CKS-duplicate-shortcut = 
    .label = Tastenkürzel duplizieren
zen-settings-CKS-reset-shortcuts = 
    .label = Auf Standard zurücksetzen
zenCKSOption-group-other = Sonstige
zenCKSOption-group-windowAndTabManagement = Fenster- & Tab-Verwaltung
zenCKSOption-group-navigation = Navigation
zenCKSOption-group-searchAndFind = Suchen & Finden
zenCKSOption-group-pageOperations = Seitenoperationen
zenCKSOption-group-historyAndBookmarks = Verlauf & Lesezeichen
zenCKSOption-group-mediaAndDisplay = Medien & Anzeige
zenCKSOption-group-zen-compact-mode = Kompaktmodus
zenCKSOption-group-zen-workspace = Arbeitsbereiche
zenCKSOption-group-zen-other = Andere Zen-Funktionen
zenCKSOption-group-zen-split-view = Geteilte Ansicht
zenCKSOption-group-devTools = Entwicklertools
zen-key-quick-restart = Schneller Neustart
zen-window-new-shortcut = Neues Fenster
zen-tab-new-shortcut = Neuer Tab
zen-key-redo = Wiederholen
zen-restore-last-closed-tab-shortcut = Letzten geschlossenen Tab wiederherstellen
zen-location-open-shortcut = Adresse öffnen
zen-location-open-shortcut-alt = Adresse öffnen (Alt)
zen-key-undo-close-window = Fenster schließen rückgängig machen
zen-text-action-undo-shortcut = Rückgängig
zen-text-action-redo-shortcut = Wiederholen
zen-text-action-cut-shortcut = Ausschneiden
zen-text-action-copy-shortcut = Kopieren
zen-text-action-copy-url-shortcut = Aktuelle URL kopieren
zen-text-action-copy-url-markdown-shortcut = Aktuelle URL als Markdown kopieren
zen-text-action-paste-shortcut = Einfügen
zen-text-action-select-all-shortcut = Alles auswählen
zen-text-action-delete-shortcut = Löschen
zen-history-show-all-shortcut-mac = Gesamten Verlauf anzeigen (Mac)
zen-full-screen-shortcut = Vollbild umschalten
zen-reader-mode-toggle-shortcut-windows = Lesemodus umschalten (Windows)
zen-picture-in-picture-toggle-shortcut-alt = Bild-in-Bild umschalten (Alt)
zen-picture-in-picture-toggle-shortcut-mac = Bild-in-Bild umschalten (Mac)
zen-picture-in-picture-toggle-shortcut-mac-alt = Bild-in-Bild umschalten (Mac Alt)
zen-page-source-shortcut-safari = Seitenquelltext anzeigen (Safari)
zen-nav-stop-shortcut = Laden stoppen
zen-history-sidebar-shortcut = Verlauf-Seitenleiste anzeigen
zen-window-minimize-shortcut = Fenster minimieren
zen-help-shortcut = Hilfe öffnen
zen-preferences-shortcut = Einstellungen öffnen
zen-hide-app-shortcut = Anwendung ausblenden
zen-hide-other-apps-shortcut = Andere Anwendungen ausblenden
zen-search-focus-shortcut = Suche fokussieren
zen-search-focus-shortcut-alt = Suche fokussieren (Alt)
zen-downloads-shortcut = Downloads öffnen
zen-addons-shortcut = Add-ons öffnen
zen-file-open-shortcut = Datei öffnen
zen-save-page-shortcut = Seite speichern
zen-print-shortcut = Seite drucken
zen-close-shortcut-2 = Tab schließen
zen-mute-toggle-shortcut = Stummschaltung umschalten
zen-key-delete = Entfernen-Taste
zen-key-go-back = Zurück gehen
zen-key-go-forward = Vorwärts gehen
zen-nav-back-shortcut-alt = Rückwärts navigieren (Alt)
zen-nav-fwd-shortcut-alt = Vorwärts navigieren (Alt)
zen-history-show-all-shortcut = Gesamten Verlauf anzeigen
zen-key-enter-full-screen = Vollbild aktivieren
zen-key-exit-full-screen = Vollbild verlassen
zen-ai-chatbot-sidebar-shortcut = KI-Chatbot-Seitenleiste umschalten
zen-key-inspector-mac = Inspektor umschalten (Mac)
zen-toggle-sidebar-shortcut = Firefox-Seitenleiste umschalten
zen-reader-mode-toggle-shortcut-other = Lesemodus umschalten
zen-picture-in-picture-toggle-shortcut = Bild-in-Bild umschalten
zen-nav-reload-shortcut-2 = Seite neu laden
zen-key-about-processes = Über Prozesse
zen-page-source-shortcut = Seitenquelltext anzeigen
zen-page-info-shortcut = Seiteninformationen anzeigen
zen-find-shortcut = Auf Seite suchen
zen-search-find-again-shortcut = Erneut suchen
zen-search-find-again-shortcut-prev = Vorheriges suchen
zen-search-find-again-shortcut-2 = Erneut suchen (Alt)
zen-bookmark-this-page-shortcut = Diese Seite zu Lesezeichen hinzufügen
zen-bookmark-show-library-shortcut = Lesezeichen-Bibliothek anzeigen
zen-key-stop = Laden stoppen
zen-full-zoom-reduce-shortcut = Herauszoomen
zen-full-zoom-enlarge-shortcut = Hineinzoomen
zen-full-zoom-reset-shortcut = Zoom zurücksetzen
zen-full-zoom-reset-shortcut-alt = Zoom zurücksetzen (Alt)
zen-full-zoom-enlarge-shortcut-alt = Hineinzoomen (Alt)
zen-full-zoom-enlarge-shortcut-alt2 = Hineinzoomen (Alt 2)
zen-bidi-switch-direction-shortcut = Textrichtung wechseln
zen-private-browsing-shortcut = Privates Surfen
zen-screenshot-shortcut = Screenshot erstellen
zen-key-sanitize = Browserdaten löschen
zen-quit-app-shortcut = Anwendung beenden
zen-key-wr-capture-cmd = WR Aufnahmebefehl
zen-key-wr-toggle-capture-sequence-cmd = WR Aufnahmesequenz umschalten
zen-nav-reload-shortcut = Seite neu laden
zen-nav-reload-shortcut-skip-cache = Seite neu laden (Cache überspringen)
zen-close-shortcut = Fenster schließen
zen-close-tab-shortcut = Tab schließen
zen-compact-mode-shortcut-show-sidebar = Schwebende Seitenleiste umschalten
zen-compact-mode-shortcut-show-toolbar = Schwebende Symbolleiste umschalten
zen-compact-mode-shortcut-toggle = Kompaktmodus umschalten
zen-workspace-shortcut-switch-1 = Zu Arbeitsbereich 1 wechseln
zen-workspace-shortcut-switch-2 = Zu Arbeitsbereich 2 wechseln
zen-workspace-shortcut-switch-3 = Zu Arbeitsbereich 3 wechseln
zen-workspace-shortcut-switch-4 = Zu Arbeitsbereich 4 wechseln
zen-workspace-shortcut-switch-5 = Zu Arbeitsbereich 5 wechseln
zen-workspace-shortcut-switch-6 = Zu Arbeitsbereich 6 wechseln
zen-workspace-shortcut-switch-7 = Zu Arbeitsbereich 7 wechseln
zen-workspace-shortcut-switch-8 = Zu Arbeitsbereich 8 wechseln
zen-workspace-shortcut-switch-9 = Zu Arbeitsbereich 9 wechseln
zen-workspace-shortcut-switch-10 = Zu Arbeitsbereich 10 wechseln
zen-workspace-shortcut-forward = Nächster Arbeitsbereich
zen-workspace-shortcut-backward = Vorheriger Arbeitsbereich
zen-sidebar-shortcut-toggle = Seitenleisten-Breite umschalten
zen-pinned-tab-shortcut-reset = Angepinnten Tab auf angepinnte URL zurücksetzen
zen-split-view-shortcut-grid = Geteilte Ansicht Raster umschalten
zen-split-view-shortcut-vertical = Geteilte Ansicht vertikal umschalten
zen-split-view-shortcut-horizontal = Geteilte Ansicht horizontal umschalten
zen-split-view-shortcut-unsplit = Geteilte Ansicht schließen
zen-key-select-tab-1 = Tab #1 auswählen
zen-key-select-tab-2 = Tab #2 auswählen
zen-key-select-tab-3 = Tab #3 auswählen
zen-key-select-tab-4 = Tab #4 auswählen
zen-key-select-tab-5 = Tab #5 auswählen
zen-key-select-tab-6 = Tab #6 auswählen
zen-key-select-tab-7 = Tab #7 auswählen
zen-key-select-tab-8 = Tab #8 auswählen
zen-key-select-tab-last = Letzten Tab auswählen
zen-key-show-all-tabs = Alle Tabs anzeigen
zen-key-goto-history = Zum Verlauf gehen
zen-key-go-home = Zur Startseite gehen
zen-bookmark-show-sidebar-shortcut = Lesezeichen-Seitenleiste anzeigen
zen-bookmark-show-toolbar-shortcut = Lesezeichen-Symbolleiste anzeigen
zen-devtools-toggle-shortcut = Entwicklertools umschalten
zen-devtools-toggle-browser-toolbox-shortcut = Browser-Toolbox umschalten
zen-devtools-toggle-browser-console-shortcut = Browser-Konsole umschalten
zen-devtools-toggle-responsive-design-mode-shortcut = Responsive Design-Modus umschalten
zen-devtools-toggle-inspector-shortcut = Inspektor umschalten
zen-devtools-toggle-web-console-shortcut = Web-Konsole umschalten
zen-devtools-toggle-js-debugger-shortcut = JavaScript-Debugger umschalten
zen-devtools-toggle-net-monitor-shortcut = Netzwerk-Monitor umschalten
zen-devtools-toggle-style-editor-shortcut = Stil-Editor umschalten
zen-devtools-toggle-performance-shortcut = Performance umschalten
zen-devtools-toggle-storage-shortcut = Speicher umschalten
zen-devtools-toggle-dom-shortcut = DOM umschalten
zen-devtools-toggle-accessibility-shortcut = Barrierefreiheit umschalten
