zen-toolbar-context-tabs-right = 
    .label = Вкладки справа
    .accesskey = Р
zen-toolbar-context-compact-mode = 
    .label = Компактный вид
    .accesskey = С
zen-toolbar-context-compact-mode-enable = 
    .label = Включить компактный вид
    .accesskey = D
zen-toolbar-context-compact-mode-just-tabs = 
    .label = Hide sidebar
zen-toolbar-context-compact-mode-just-toolbar = 
    .label = Скрыть панель инструментов
zen-toolbar-context-compact-mode-hide-both = 
    .label = Скрыть оба
    .accesskey = Н
zen-toolbar-context-new-folder = 
    .label = Новая папка
    .accesskey = N
sidebar-zen-expand = 
    .label = Развернуть боковую панель
sidebar-zen-create-new = 
    .label = Создать новый...
tabbrowser-unload-tab-button = 
    .tooltiptext =
        { $tabCount ->
            [one] Выгрузить и переключиться на вкладку
           *[other] Выгрузить { $tabCount } вкладки(-ок) и переключиться на первую
        }
tabbrowser-reset-pin-button = 
    .tooltiptext =
        { $tabCount ->
            [one] Сбросить и закрепить вкладку
           *[other] Сбросить и закрепить { $tabCount } вкладки(-ок)
        }
tab-reset-pin-label = Вернуться к закреплённому адресу
