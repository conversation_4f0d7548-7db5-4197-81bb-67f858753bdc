pane-zen-looks-title = Внешний вид
category-zen-looks = 
    .tooltiptext = { pane-zen-looks-title }
zen-warning-language = Изменение языка по умолчанию может упростить сайтам слежку за вами.
zen-vertical-tabs-layout-header = Макет браузера
zen-vertical-tabs-layout-description = Выберите макет, который подходит именно вам
zen-layout-single-toolbar = Всё в одной панели
zen-layout-multiple-toolbar = Несколько панелей
zen-layout-collapsed-toolbar = Компактный список вкладок
sync-currently-syncing-workspaces = Рабочие пространства
sync-engine-workspaces = 
    .label = Рабочие пространства
    .tooltiptext = Синхронизируйте пространства между устройствами
    .accesskey = W
zen-glance-title = Предпросмотр
zen-glance-header = Общие настройки предпросмотра
zen-glance-description = Быстро просматривайте ссылки, не открывая их в отдельной вкладке
zen-glance-trigger-label = Способ активации
zen-glance-enabled = 
    .label = Включить предпросмотр
zen-glance-trigger-ctrl-click = 
    .label = Ctrl + ЛКМ
zen-glance-trigger-alt-click = 
    .label = Alt + ЛКМ
zen-glance-trigger-shift-click = 
    .label = Shift + ЛКМ
zen-glance-trigger-meta-click = 
    .label = Meta (Command) + ЛКМ
zen-glance-trigger-mantain-click = 
    .label = Зажатие ЛКМ (скоро!)
zen-look-and-feel-compact-view-header = Компактный вид
zen-look-and-feel-compact-view-description = Включите только нужные панели инструментов!
zen-look-and-feel-compact-view-enabled = 
    .label = Включить компактный вид { -brand-short-name }
zen-look-and-feel-compact-view-top-toolbar = 
    .label = Скрыть верхнюю панель инструментов в компактном виде
zen-look-and-feel-compact-toolbar-flash-popup = 
    .label = Временно отображать панель инструментов при переключении или открытии новых вкладок в компактном виде
pane-zen-tabs-title = Управление вкладками
category-zen-workspaces = 
    .tooltiptext = { pane-zen-tabs-title }
pane-settings-workspaces-title = Рабочие пространства
zen-tabs-unloader-enabled = 
    .label = Включить выгрузку вкладок
zen-look-and-feel-compact-toolbar-themed = 
    .label = Использовать цвета темы для компактной панели инструментов
zen-look-and-feel-compact-sidebar-themed = 
    .label = Использовать цвета темы для компактной боковой панели
zen-workspace-continue-where-left-off = 
    .label = Продолжить с того места, где вы остановились
pane-zen-pinned-tab-manager-title = Закреплённые вкладки
zen-pinned-tab-manager-header = Настройки закреплённых вкладок
zen-pinned-tab-manager-description = Задайте дополнительные настройки поведения закреплённых вкладок
zen-pinned-tab-manager-restore-pinned-tabs-to-pinned-url = 
    .label = Восстанавливать закреплённые вкладки с исходным адресом при запуске браузера
zen-pinned-tab-manager-container-specific-essentials-enabled = 
    .label = Включить специфические для контейнеров важные страницы
zen-pinned-tab-manager-close-shortcut-behavior-label = При закрытии вкладки с помощью горячей клавиши
zen-pinned-tab-manager-reset-unload-switch-close-shortcut-option = 
    .label = Восстановить исходный адрес, выгрузить и переключиться на следующую вкладку
zen-pinned-tab-manager-unload-switch-close-shortcut-option = 
    .label = Выгрузить и переключиться на следующую вкладку
zen-pinned-tab-manager-reset-switch-close-shortcut-option = 
    .label = Восстановить исходный адрес и переключиться на следующую вкладку
zen-pinned-tab-manager-switch-close-shortcut-option = 
    .label = Переключиться на следующую вкладку
zen-pinned-tab-manager-reset-close-shortcut-option = 
    .label = Восстановить исходный адрес
zen-pinned-tab-manager-close-close-shortcut-option = 
    .label = Закрыть вкладку
pane-zen-workspaces-header = Рабочие пространства
zen-settings-workspaces-header = Настройки рабочих пространств
zen-settings-workspaces-description = С помощью рабочих пространств вы можете вести несколько сеансов одновременно!
zen-settings-workspaces-enabled = 
    .label = Включить рабочие пространства
zen-settings-workspaces-hide-default-container-indicator = 
    .label = Скрывать индикатор контейнера по умолчанию на панели вкладок
zen-key-unsaved = Вы не сохранили сочетание клавиш. Сохраните его, нажав Esc после ввода.
zen-key-conflict = Конфликт с другим сочетанием клавиш
pane-zen-theme-title = Настройки темы
zen-vertical-tabs-title = Боковая панель и раскладка вкладок
zen-vertical-tabs-header = Вертикальные вкладки
zen-vertical-tabs-description = Управляйте вкладками в вертикальном виде
zen-vertical-tabs-show-expand-button = 
    .label = Показывать кнопку «Развернуть»
zen-vertical-tabs-newtab-on-tab-list = 
    .label = Показывать кнопку «Новая вкладка»
zen-vertical-tabs-newtab-top-button-up = 
    .label = Переместить кнопку «Новая вкладка» наверх
zen-vertical-tabs-expand-tabs-by-default = Раскрывать вкладки по умолчанию
zen-vertical-tabs-dont-expand-tabs-by-default = Не раскрывать вкладки по умолчанию
zen-vertical-tabs-expand-tabs-on-hover = Раскрывать вкладки при наведении (не работает в компактном виде)
zen-vertical-tabs-expand-tabs-header = Способы раскрытия вкладок
zen-vertical-tabs-expand-tabs-description = Выберите, как именно раскрывать вкладки на боковой панели
zen-theme-marketplace-header = Моды для Zen
zen-theme-disable-all-enabled = 
    .title = Отключить все моды
zen-theme-disable-all-disabled = 
    .title = Включить все моды
zen-theme-marketplace-description = Находите и устанавливайте моды из магазина.
zen-theme-marketplace-remove-button = 
    .label = Удалить мод
zen-theme-marketplace-check-for-updates-button = 
    .label = Проверить наличие обновлений
zen-theme-marketplace-import-button = 
    .label = Импортировать моды
zen-theme-marketplace-export-button = 
    .label = Экспортировать моды
zen-theme-marketplace-import-success = Моды успешно импортированы
zen-theme-marketplace-import-failure = Во время импортирования модов произошла ошибка
zen-theme-marketplace-export-success = Моды успешно импортированы
zen-theme-marketplace-export-failure = Во время экспортирования модов произошла ошибка
zen-theme-marketplace-updates-success = Моды успешно обновлены
zen-theme-marketplace-updates-failure = Обновлений не найдено!
zen-theme-marketplace-toggle-enabled-button = 
    .title = Отключить мод
zen-theme-marketplace-toggle-disabled-button = 
    .title = Включить мод
zen-theme-marketplace-remove-confirmation = Вы уверены, что хотите удалить этот мод?
zen-theme-marketplace-close-modal = Закрыть
zen-theme-marketplace-theme-header-title = 
    .title = Селектор CSS: { $name }
zen-theme-marketplace-dropdown-default-label = 
    .label = Нет
zen-theme-marketplace-input-default-placeholder = 
    .placeholder = Напишите что-нибудь…
pane-zen-marketplace-title = Моды для Zen
zen-themes-auto-update = 
    .label = Автоматически обновлять установленные моды при запуске
zen-settings-workspaces-force-container-tabs-to-workspace = 
    .label = Переключиться на пространство, где контейнер установлен по умолчанию при открытии вкладок контейнера
zen-theme-marketplace-link = Посетить магазин
zen-dark-theme-styles-header = Стили тёмной темы
zen-dark-theme-styles-description = Настройте тёмную тему на свой лад.
zen-dark-theme-styles-amoled = Ночная тема
zen-dark-theme-styles-default = Тёмная тема по умолчанию
zen-dark-theme-styles-colorful = Красочная тёмная тема
zen-compact-mode-styles-left = Скрыть панель вкладок
zen-compact-mode-styles-top = Скрыть верхнюю панель
zen-compact-mode-styles-both = Скрыть обе панели
zen-urlbar-title = Поле адреса Zen
zen-urlbar-header = Общие настройки поля адреса
zen-urlbar-description = Настройте поле адреса на свой лад
zen-urlbar-behavior-label = Поведение
zen-urlbar-behavior-normal = 
    .label = Обычное
zen-urlbar-behavior-floating-on-type = 
    .label = Всплывает при набирании текста
zen-urlbar-behavior-float = 
    .label = Всегда всплывает
pane-zen-CKS-title = Горячие клавиши
category-zen-CKS = 
    .tooltiptext = { pane-zen-CKS-title }
pane-settings-CKS-title = Горячие клавиши { -brand-short-name }
zen-settings-CKS-header = Настроить сочетания клавиш
zen-settings-CKS-description = Измените комбинации клавиш по умолчанию по своему вкусу и улучшите качество работы в браузере.
zen-settings-CKS-disable-firefox = 
    .label = Отключить стандартные сочетания клавиш { -brand-short-name }
zen-settings-CKS-duplicate-shortcut = 
    .label = Дублировать комбинацию клавиш
zen-settings-CKS-reset-shortcuts = 
    .label = Сброс по умолчанию
zenCKSOption-group-other = Прочие вопросы
zenCKSOption-group-windowAndTabManagement = Управление окном и вкладками
zenCKSOption-group-navigation = Навигация
zenCKSOption-group-searchAndFind = Поиск
zenCKSOption-group-pageOperations = Операции страницы
zenCKSOption-group-historyAndBookmarks = История и закладки
zenCKSOption-group-mediaAndDisplay = Медиа и дисплей
zenCKSOption-group-zen-compact-mode = Компактный вид
zenCKSOption-group-zen-workspace = Рабочие пространства
zenCKSOption-group-zen-other = Другие функции Zen
zenCKSOption-group-zen-split-view = Разделённый вид
zenCKSOption-group-devTools = Инструменты разработчика
zen-key-quick-restart = Быстрый перезапуск
zen-window-new-shortcut = Новое окно
zen-tab-new-shortcut = Создать новую вкладку
zen-key-redo = Повторить
zen-restore-last-closed-tab-shortcut = Восстановить последнюю закрытую вкладку
zen-location-open-shortcut = Открыть местоположение
zen-location-open-shortcut-alt = Открыть местоположение (Alt)
zen-key-undo-close-window = Восстановить закрытое окно
zen-text-action-undo-shortcut = Отменить
zen-text-action-redo-shortcut = Повторить
zen-text-action-cut-shortcut = Вырезать
zen-text-action-copy-shortcut = Скопировать
zen-text-action-copy-url-shortcut = Скопировать текущую ссылку
zen-text-action-copy-url-markdown-shortcut = Скопировать текущую ссылку как Markdown
zen-text-action-paste-shortcut = Вставить
zen-text-action-select-all-shortcut = Выделить все
zen-text-action-delete-shortcut = Удалить
zen-history-show-all-shortcut-mac = Показать всю историю (Mac)
zen-full-screen-shortcut = Вкл./выкл. полноэкранный режим
zen-reader-mode-toggle-shortcut-windows = Переключить режим чтения (Windows)
zen-picture-in-picture-toggle-shortcut-alt = Переключить изображение в картинке (Alt)
zen-picture-in-picture-toggle-shortcut-mac = Переключить изображение в изображение (Mac)
zen-picture-in-picture-toggle-shortcut-mac-alt = Переключить изображение в картинке (Mac Alt)
zen-page-source-shortcut-safari = Просмотр исходного кода страницы (Safari)
zen-nav-stop-shortcut = Остановить загрузку
zen-history-sidebar-shortcut = Show History Sidebar
zen-window-minimize-shortcut = Свернуть окно
zen-help-shortcut = Открыть справку
zen-preferences-shortcut = Открыть настройки
zen-hide-app-shortcut = Скрыть приложение
zen-hide-other-apps-shortcut = Скрыть другие приложения
zen-search-focus-shortcut = Поиск в фокусе
zen-search-focus-shortcut-alt = Сфокусироваться на поисковое поле (Alt)
zen-downloads-shortcut = Открыть загрузки
zen-addons-shortcut = Открыть дополнения
zen-file-open-shortcut = Открыть файл
zen-save-page-shortcut = Сохранить страницу
zen-print-shortcut = Распечатать страницу
zen-close-shortcut-2 = Закрыть вкладку
zen-mute-toggle-shortcut = Включить/выключить звук
zen-key-delete = Удалить ключ
zen-key-go-back = Назад
zen-key-go-forward = Вперед
zen-nav-back-shortcut-alt = Назад (Alt)
zen-nav-fwd-shortcut-alt = Переход вперед (Alt)
zen-history-show-all-shortcut = Показать полную историю
zen-key-enter-full-screen = Перейти в полноэкранный режим
zen-key-exit-full-screen = Выйти из полноэкранного режима
zen-ai-chatbot-sidebar-shortcut = Переключить боковую панель ИИ чат-бота
zen-key-inspector-mac = Переключить инспектор (Mac)
zen-toggle-sidebar-shortcut = Переключить боковую панель Firefox
zen-reader-mode-toggle-shortcut-other = Переключить режим чтения
zen-picture-in-picture-toggle-shortcut = Переключить изображение в картинке
zen-nav-reload-shortcut-2 = Обновить страницу
zen-key-about-processes = О процессах
zen-page-source-shortcut = Исходный код страницы
zen-page-info-shortcut = Просмотр информации о странице
zen-find-shortcut = Найти на странице
zen-search-find-again-shortcut = Найти следующее
zen-search-find-again-shortcut-prev = Найти предыдущий
zen-search-find-again-shortcut-2 = Найти снова (Alt)
zen-bookmark-this-page-shortcut = Добавить эту страницу в закладки
zen-bookmark-show-library-shortcut = Показать библиотеку закладок
zen-key-stop = Остановить загрузку
zen-full-zoom-reduce-shortcut = Уменьшить
zen-full-zoom-enlarge-shortcut = Увеличить
zen-full-zoom-reset-shortcut = Сбросить масштаб
zen-full-zoom-reset-shortcut-alt = Сбросить масштаб (Alt)
zen-full-zoom-enlarge-shortcut-alt = Увеличить масштаб (Alt)
zen-full-zoom-enlarge-shortcut-alt2 = Увеличить масштаб (вторая Alt)
zen-bidi-switch-direction-shortcut = Переключить направление текста
zen-private-browsing-shortcut = Приватный просмотр
zen-screenshot-shortcut = Сделать скриншот
zen-key-sanitize = Очистить данные браузера
zen-quit-app-shortcut = Выйти из приложения
zen-key-wr-capture-cmd = Команда захвата WR
zen-key-wr-toggle-capture-sequence-cmd = Переключить последовательность захвата WR
zen-nav-reload-shortcut = Обновить страницу
zen-nav-reload-shortcut-skip-cache = Обновить страницу (пропустить кэш)
zen-close-shortcut = Закрыть окно
zen-close-tab-shortcut = Закрыть вкладку
zen-compact-mode-shortcut-show-sidebar = Включить/выключить плавающую боковую панель
zen-compact-mode-shortcut-show-toolbar = Инструменты плавающей панели
zen-compact-mode-shortcut-toggle = Компактный режим
zen-workspace-shortcut-switch-1 = Переключиться на пространство 1
zen-workspace-shortcut-switch-2 = Переключиться на пространство 2
zen-workspace-shortcut-switch-3 = Переключиться на пространство 3
zen-workspace-shortcut-switch-4 = Переключиться на пространство 4
zen-workspace-shortcut-switch-5 = Переключиться на пространство 5
zen-workspace-shortcut-switch-6 = Переключиться на пространство 6
zen-workspace-shortcut-switch-7 = Переключиться на пространство 7
zen-workspace-shortcut-switch-8 = Переключиться на пространство 8
zen-workspace-shortcut-switch-9 = Переключиться на пространство 9
zen-workspace-shortcut-switch-10 = Переключиться на пространство 10
zen-workspace-shortcut-forward = Следующее пространство
zen-workspace-shortcut-backward = Предыдущее пространство
zen-sidebar-shortcut-toggle = Ширина боковой панели
zen-pinned-tab-shortcut-reset = Восстановить адрес закрепленной вкладки
zen-split-view-shortcut-grid = Переключить разделение сетки вида
zen-split-view-shortcut-vertical = Вертикальный режим разделения
zen-split-view-shortcut-horizontal = Горизонтальное разделение
zen-split-view-shortcut-unsplit = Закрыть раздельный вид
zen-key-select-tab-1 = Выбрать вкладку #1
zen-key-select-tab-2 = Выбрать вкладку #2
zen-key-select-tab-3 = Выбрать вкладку #3
zen-key-select-tab-4 = Выбрать вкладку #4
zen-key-select-tab-5 = Выбрать вкладку №5
zen-key-select-tab-6 = Выбрать вкладку #6
zen-key-select-tab-7 = Выбрать вкладку №7
zen-key-select-tab-8 = Выбрать вкладку #8
zen-key-select-tab-last = Выбрать последнюю вкладку
zen-key-show-all-tabs = Показать все вкладки
zen-key-goto-history = Перейти к истории
zen-key-go-home = На главную
zen-bookmark-show-sidebar-shortcut = Показать боковую панель закладок
zen-bookmark-show-toolbar-shortcut = Показать панель закладок
zen-devtools-toggle-shortcut = Переключить DevTools
zen-devtools-toggle-browser-toolbox-shortcut = Переключить панель браузера
zen-devtools-toggle-browser-console-shortcut = Переключить консоль браузера
zen-devtools-toggle-responsive-design-mode-shortcut = Переключить адаптивный дизайн
zen-devtools-toggle-inspector-shortcut = Переключить инспектор
zen-devtools-toggle-web-console-shortcut = Включить/выключить веб-консоль
zen-devtools-toggle-js-debugger-shortcut = Переключить отладчик JavaScript
zen-devtools-toggle-net-monitor-shortcut = Переключить сетевой монитор
zen-devtools-toggle-style-editor-shortcut = Переключить редактор стилей
zen-devtools-toggle-performance-shortcut = Переключить производительность
zen-devtools-toggle-storage-shortcut = Переключить хранилище
zen-devtools-toggle-dom-shortcut = Переключить DOM
zen-devtools-toggle-accessibility-shortcut = Включить/выключить специальные возможности
