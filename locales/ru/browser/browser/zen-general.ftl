zen-panel-ui-current-profile-text = текущий профиль
unified-extensions-description = Расширения дополняют функционал { -brand-short-name }.
tab-context-zen-reset-pinned-tab = 
    .label = Сбросить закреплённую вкладку
    .accesskey = R
tab-context-zen-add-essential = 
    .label = Добавить в важное ({ $num } из 12 слотов заполнено)
    .accesskey = E
tab-context-zen-remove-essential = 
    .label = Удалить из важного
    .accesskey = R
tab-context-zen-replace-pinned-url-with-current = 
    .label = Заменить закреплённый адрес на текущий
    .accesskey = C
zen-themes-corrupted = Файл дополнения { -brand-short-name } повреждён. Возвращена тема по умолчанию.
zen-shortcuts-corrupted = Файл комбинаций клавиш { -brand-short-name } повреждён. Возвращены комбинации клавиш по умолчанию.
# note: Do not translate the "<br/>" tags in the following string
zen-new-urlbar-notification =
    Новая адресная строка активирована, теперь нет необходимости использовать отдельные страницы для новых вкладок.<br/><br/>
    Попробуйте открыть новую вкладку, чтобы увидеть новую адресную строку в действии!
zen-disable = Выключить
pictureinpicture-minimize-btn = 
    .aria-label = Свернуть
    .tooltip = Свернуть
zen-panel-ui-gradient-generator-custom-color = Пользовательский цвет
zen-panel-ui-gradient-generator-saved-message = Градиент успешно сохранён!
zen-copy-current-url-confirmation = Ссылка скопирована в буфер обмена.
zen-general-cancel-label = 
    .label = Отменить
zen-general-confirm = 
    .label = Подтвердить
zen-pinned-tab-replaced = Адрес закреплённой вкладки заменён на текущий адрес!
zen-tabs-renamed = Вкладка успешно переименована!
zen-background-tab-opened-toast = Открыта новая фоновая вкладка!
zen-workspace-renamed-toast = Пространство успешно переименовано!
zen-library-sidebar-workspaces = 
    .label = Пространства
zen-library-sidebar-mods = 
    .label = Моды

# note: Do not translate the "<br/>" tags in the following string

zen-learn-more-text = Узнать больше
zen-close-label = Закрыть
zen-singletoolbar-urlbar-placeholder-with-name = 
    .placeholder = Найти...
zen-icons-picker-emoji = 
    .label = Emojis
zen-icons-picker-svg = 
    .label = Icons
