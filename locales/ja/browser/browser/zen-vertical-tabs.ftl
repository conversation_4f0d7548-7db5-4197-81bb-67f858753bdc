zen-toolbar-context-tabs-right = 
    .label = タブバーを右側に表示
    .accesskey = R
zen-toolbar-context-compact-mode = 
    .label = コンパクトモード
    .accesskey = C
zen-toolbar-context-compact-mode-enable = 
    .label = コンパクトモードを有効にする
    .accesskey = D
zen-toolbar-context-compact-mode-just-tabs = 
    .label = サイドバーを隠す
zen-toolbar-context-compact-mode-just-toolbar = 
    .label = ツールバーを隠す
zen-toolbar-context-compact-mode-hide-both = 
    .label = 両方隠す
    .accesskey = H
zen-toolbar-context-new-folder = 
    .label = New folder
    .accesskey = N
sidebar-zen-expand = 
    .label = サイドバーを展開する
sidebar-zen-create-new = 
    .label = Create New...
tabbrowser-unload-tab-button = 
    .tooltiptext =
        { $tabCount ->
            [one] Unload and switch to tab
           *[other] Unload { $tabCount } tabs and switch to the first
        }
tabbrowser-reset-pin-button = 
    .tooltiptext =
        { $tabCount ->
            [one] Reset and pin tab
           *[other] Reset and pin { $tabCount } tabs
        }
tab-reset-pin-label = ピン留めしたURLに戻る
