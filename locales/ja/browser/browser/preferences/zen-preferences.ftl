pane-zen-looks-title = 外観
category-zen-looks = 
    .tooltiptext = { pane-zen-looks-title }
zen-warning-language = デフォルト言語を変更すると、ウェブサイトがあなたを追跡しやすくなります。
zen-vertical-tabs-layout-header = ブラウザーのレイアウト
zen-vertical-tabs-layout-description = 最適なレイアウトを選択
zen-layout-single-toolbar = 単一ツールバー
zen-layout-multiple-toolbar = 複数ツールバー
zen-layout-collapsed-toolbar = 折りたたみツールバー
sync-currently-syncing-workspaces = ワークスペース
sync-engine-workspaces = 
    .label = ワークスペース
    .tooltiptext = 端末間でワークスペースを同期
    .accesskey = W
zen-glance-title = Glance
zen-glance-header = 一般的な設定
zen-glance-description = 新しいタブで開かずにリンクの簡単な概要を取得します
zen-glance-trigger-label = トリガーメソッド
zen-glance-enabled = 
    .label = Glanceを有効にする
zen-glance-trigger-ctrl-click = 
    .label = Ctrl + Click
zen-glance-trigger-alt-click = 
    .label = Alt + Click
zen-glance-trigger-shift-click = 
    .label = Shift + クリック
zen-glance-trigger-meta-click = 
    .label = Meta (Command) + クリック
zen-glance-trigger-mantain-click = 
    .label = クリックを長押し(近日公開予定)
zen-look-and-feel-compact-view-header = コンパクトビューで表示
zen-look-and-feel-compact-view-description = 使用しているツールバーのみを表示します！
zen-look-and-feel-compact-view-enabled = 
    .label = { -brand-short-name }のコンパクトモードを有効にする
zen-look-and-feel-compact-view-top-toolbar = 
    .label = 上部のツールバーをコンパクトモードで非表示にする
zen-look-and-feel-compact-toolbar-flash-popup = 
    .label = コンパクトモードで新しいタブを切り替えたり開いたりするときに、簡単にツールバーをポップアップさせる
pane-zen-tabs-title = タブ管理
category-zen-workspaces = 
    .tooltiptext = { pane-zen-tabs-title }
pane-settings-workspaces-title = ワークスペース
zen-tabs-unloader-enabled = 
    .label = タブのアンローダーを有効にする
zen-look-and-feel-compact-toolbar-themed = 
    .label = コンパクトなツールバーにテーマの背景を使用する
zen-look-and-feel-compact-sidebar-themed = 
    .label = コンパクトなサイドバーにテーマの背景を使用する
zen-workspace-continue-where-left-off = 
    .label = Continue where you left off
pane-zen-pinned-tab-manager-title = ピン留めされたタブ
zen-pinned-tab-manager-header = ピン留めされたタブの設定
zen-pinned-tab-manager-description = ピン留めされたタブの追加動作を管理
zen-pinned-tab-manager-restore-pinned-tabs-to-pinned-url = 
    .label = ピン留めされたタブを起動時に元のピン留めされた URL に復元します
zen-pinned-tab-manager-container-specific-essentials-enabled = 
    .label = Enable container-specific essentials
zen-pinned-tab-manager-close-shortcut-behavior-label = タブを閉じる ショートカットの動作
zen-pinned-tab-manager-reset-unload-switch-close-shortcut-option = 
    .label = URLをリセットし、アンロードして次のタブに切り替える
zen-pinned-tab-manager-unload-switch-close-shortcut-option = 
    .label = アンロードし、次のタブに切り替える
zen-pinned-tab-manager-reset-switch-close-shortcut-option = 
    .label = URLをリセットし次のタブに切り替える
zen-pinned-tab-manager-switch-close-shortcut-option = 
    .label = 次のタブに切り替える
zen-pinned-tab-manager-reset-close-shortcut-option = 
    .label = URLをリセット
zen-pinned-tab-manager-close-close-shortcut-option = 
    .label = タブを閉じる
pane-zen-workspaces-header = ワークスペース
zen-settings-workspaces-header = ワークスペースの一般設定
zen-settings-workspaces-description = ワークスペースを使用すると、一度に複数のブラウジングセッションを行うことができます!
zen-settings-workspaces-enabled = 
    .label = ワークスペースを有効にする (実験的)
zen-settings-workspaces-hide-default-container-indicator = 
    .label = タブバー内のデフォルトのコンテナインジケーターを非表示にする
zen-key-unsaved = 未保存のショートカットです！再入力後、「エスケープ」キーをクリックして安全にしてください。
zen-key-conflict = 他のショートカットと競合します
pane-zen-theme-title = テーマ設定
zen-vertical-tabs-title = サイドバーとタブのレイアウト
zen-vertical-tabs-header = 垂直タブ
zen-vertical-tabs-description = 垂直レイアウトでタブを管理
zen-vertical-tabs-show-expand-button = 
    .label = 展開ボタンを表示
zen-vertical-tabs-newtab-on-tab-list = 
    .label = 「新しいタブ」ボタンを表示
zen-vertical-tabs-newtab-top-button-up = 
    .label = 「新しいタブ」ボタンを一番上に表示
zen-vertical-tabs-expand-tabs-by-default = デフォルトでタブを展開
zen-vertical-tabs-dont-expand-tabs-by-default = デフォルトでタブを展開しない
zen-vertical-tabs-expand-tabs-on-hover = ホバーでタブを展開 (コンパクトモードでは動作しません)
zen-vertical-tabs-expand-tabs-header = タブを展開する方法
zen-vertical-tabs-expand-tabs-description = サイドバーのタブを展開する方法を選択します
zen-theme-marketplace-header = Zen Mods
zen-theme-disable-all-enabled = 
    .title = すべてのModを無効化
zen-theme-disable-all-disabled = 
    .title = すべてのModを有効化
zen-theme-marketplace-description = ストアからModをインストール
zen-theme-marketplace-remove-button = 
    .label = Modを削除
zen-theme-marketplace-check-for-updates-button = 
    .label = アップデートの確認
zen-theme-marketplace-import-button = 
    .label = Import mods
zen-theme-marketplace-export-button = 
    .label = Export Mods
zen-theme-marketplace-import-success = Mods imported successfully
zen-theme-marketplace-import-failure = There was an error importing the mods
zen-theme-marketplace-export-success = Mods exported successfully
zen-theme-marketplace-export-failure = There was an error exporting the mods
zen-theme-marketplace-updates-success = Modが正常に更新されました
zen-theme-marketplace-updates-failure = Couldn't find any updates!
zen-theme-marketplace-toggle-enabled-button = 
    .title = Modを無効化
zen-theme-marketplace-toggle-disabled-button = 
    .title = Modを有効化
zen-theme-marketplace-remove-confirmation = このModを削除してもよろしいですか？
zen-theme-marketplace-close-modal = 閉じる
zen-theme-marketplace-theme-header-title = 
    .title = CSS セレクター: { $name }
zen-theme-marketplace-dropdown-default-label = 
    .label = なし
zen-theme-marketplace-input-default-placeholder = 
    .placeholder = ここに入力...
pane-zen-marketplace-title = Zen Mods
zen-themes-auto-update = 
    .label = Automatically update installed mods on startup
zen-settings-workspaces-force-container-tabs-to-workspace = 
    .label = コンテナタブを開くときにコンテナがデフォルトとして設定されているワークスペースに切り替える
zen-theme-marketplace-link = ストアを訪問
zen-dark-theme-styles-header = ダークテーマのスタイル
zen-dark-theme-styles-description = 好みに合わせてダークテーマをカスタマイズする
zen-dark-theme-styles-amoled = 夜のテーマ
zen-dark-theme-styles-default = デフォルトのダークテーマ
zen-dark-theme-styles-colorful = カラフルなダークテーマ
zen-compact-mode-styles-left = タブバーを隠す
zen-compact-mode-styles-top = 上部バーを非表示
zen-compact-mode-styles-both = 両方を隠す
zen-urlbar-title = Zen URL Bar
zen-urlbar-header = URLバーの一般設定
zen-urlbar-description = 好みに合わせてURLバーをカスタマイズ
zen-urlbar-behavior-label = 動作
zen-urlbar-behavior-normal = 
    .label = 通常
zen-urlbar-behavior-floating-on-type = 
    .label = 入力時にのみフローティング
zen-urlbar-behavior-float = 
    .label = 常にフローティング
pane-zen-CKS-title = キーボードショートカット
category-zen-CKS = 
    .tooltiptext = { pane-zen-CKS-title }
pane-settings-CKS-title = { -brand-short-name } キーボードショートカット
zen-settings-CKS-header = キーボードショートカットをカスタマイズする
zen-settings-CKS-description = デフォルトのキーボードショートカットを好みに変更し、ブラウジング体験を向上させましょう
zen-settings-CKS-disable-firefox = 
    .label = { -brand-short-name }のデフォルトのキーボードショートカットを無効にする
zen-settings-CKS-duplicate-shortcut = 
    .label = ショートカットを複製
zen-settings-CKS-reset-shortcuts = 
    .label = デフォルトに戻す
zenCKSOption-group-other = その他
zenCKSOption-group-windowAndTabManagement = ウィンドウとタブ管理
zenCKSOption-group-navigation = Navigation
zenCKSOption-group-searchAndFind = 検索と検索
zenCKSOption-group-pageOperations = ページ操作
zenCKSOption-group-historyAndBookmarks = 履歴とブックマーク
zenCKSOption-group-mediaAndDisplay = メディアと表示
zenCKSOption-group-zen-compact-mode = コンパクトモード
zenCKSOption-group-zen-workspace = ワークスペース
zenCKSOption-group-zen-other = その他の禅の特徴
zenCKSOption-group-zen-split-view = 分割ビュー
zenCKSOption-group-devTools = Developer Tools
zen-key-quick-restart = すぐに再起動
zen-window-new-shortcut = 新しいウィンドウ
zen-tab-new-shortcut = 新しいタブ
zen-key-redo = やり直し
zen-restore-last-closed-tab-shortcut = 最後に閉じたタブを復元
zen-location-open-shortcut = 場所を開く
zen-location-open-shortcut-alt = 場所を開く (Alt)
zen-key-undo-close-window = 閉じたウィンドウを元に戻す
zen-text-action-undo-shortcut = 元に戻す
zen-text-action-redo-shortcut = Redo
zen-text-action-cut-shortcut = 切り取り
zen-text-action-copy-shortcut = コピー
zen-text-action-copy-url-shortcut = 現在のURLをコピー
zen-text-action-copy-url-markdown-shortcut = 現在のURLをMarkdownとしてコピー
zen-text-action-paste-shortcut = 貼り付け
zen-text-action-select-all-shortcut = すべて選択
zen-text-action-delete-shortcut = 削除
zen-history-show-all-shortcut-mac = すべての履歴を表示(Mac)
zen-full-screen-shortcut = 全画面表示の切り替え
zen-reader-mode-toggle-shortcut-windows = リーダーモードの切り替え (Windows)
zen-picture-in-picture-toggle-shortcut-alt = ピクチャーインピクチャーの切り替え (Alt)
zen-picture-in-picture-toggle-shortcut-mac = ピクチャーインピクチャー（Mac）の切り替え
zen-picture-in-picture-toggle-shortcut-mac-alt = ピクチャーインピクチャーの切り替え (Mac Alt)
zen-page-source-shortcut-safari = ページソースの表示 (Safari)
zen-nav-stop-shortcut = 読み込みを停止
zen-history-sidebar-shortcut = Show History Sidebar
zen-window-minimize-shortcut = ウィンドウを最小化する
zen-help-shortcut = ヘルプを開く
zen-preferences-shortcut = 設定を開く
zen-hide-app-shortcut = アプリケーションを隠す
zen-hide-other-apps-shortcut = 他のアプリを隠す
zen-search-focus-shortcut = 検索欄にフォーカス
zen-search-focus-shortcut-alt = 検索欄にフォーカス (Alt)
zen-downloads-shortcut = ダウンロードを開く
zen-addons-shortcut = アドオンを開く
zen-file-open-shortcut = ファイルを開く
zen-save-page-shortcut = ページを保存
zen-print-shortcut = ページを印刷
zen-close-shortcut-2 = タブを閉じる
zen-mute-toggle-shortcut = ミュートの切り替え
zen-key-delete = キーを削除
zen-key-go-back = 戻る
zen-key-go-forward = 前に進む
zen-nav-back-shortcut-alt = 戻る(Alt)
zen-nav-fwd-shortcut-alt = 前方に移動 (Alt)
zen-history-show-all-shortcut = すべての履歴を表示
zen-key-enter-full-screen = 全画面表示にする
zen-key-exit-full-screen = 全画面表示を終了
zen-ai-chatbot-sidebar-shortcut = AIチャットボットサイドバーの切り替え
zen-key-inspector-mac = インスペクタの切り替え (Mac)
zen-toggle-sidebar-shortcut = Firefoxサイドバーの切り替え
zen-reader-mode-toggle-shortcut-other = リーダーモードの切り替え
zen-picture-in-picture-toggle-shortcut = ピクチャーインピクチャーの切り替え
zen-nav-reload-shortcut-2 = ページを再読み込み
zen-key-about-processes = プロセスについて
zen-page-source-shortcut = ページソースの表示
zen-page-info-shortcut = ページ情報を表示
zen-find-shortcut = ページ上で検索
zen-search-find-again-shortcut = 再検索
zen-search-find-again-shortcut-prev = 前を検索
zen-search-find-again-shortcut-2 = 再検索 (Alt)
zen-bookmark-this-page-shortcut = このページをブックマーク
zen-bookmark-show-library-shortcut = ブックマークライブラリを表示
zen-key-stop = 読み込みを中止
zen-full-zoom-reduce-shortcut = 縮小
zen-full-zoom-enlarge-shortcut = 拡大
zen-full-zoom-reset-shortcut = 等倍
zen-full-zoom-reset-shortcut-alt = Reset Zoom (Alt)
zen-full-zoom-enlarge-shortcut-alt = Zoom In (Alt)
zen-full-zoom-enlarge-shortcut-alt2 = Zoom In (Alt 2)
zen-bidi-switch-direction-shortcut = テキストの方向を切り替える
zen-private-browsing-shortcut = プライベート閲覧
zen-screenshot-shortcut = スクリーンショットを撮影
zen-key-sanitize = ブラウジングデータをクリア
zen-quit-app-shortcut = アプリケーションを終了
zen-key-wr-capture-cmd = WRキャプチャコマンド
zen-key-wr-toggle-capture-sequence-cmd = WRキャプチャシーケンスの切り替え
zen-nav-reload-shortcut = ページを再読み込み
zen-nav-reload-shortcut-skip-cache = キャッシュを削除してページを再読み込み
zen-close-shortcut = ウィンドウを閉じる
zen-close-tab-shortcut = タブを閉じる
zen-compact-mode-shortcut-show-sidebar = フローティングサイドバーの切り替え
zen-compact-mode-shortcut-show-toolbar = フローティングツールバーの切り替え
zen-compact-mode-shortcut-toggle = コンパクトモードの切り替え
zen-workspace-shortcut-switch-1 = ワークスペース1に切り替える
zen-workspace-shortcut-switch-2 = ワークスペース2に切り替える
zen-workspace-shortcut-switch-3 = ワークスペース3に切り替える
zen-workspace-shortcut-switch-4 = ワークスペース4に切り替える
zen-workspace-shortcut-switch-5 = ワークスペース5に切り替える
zen-workspace-shortcut-switch-6 = ワークスペース6に切り替える
zen-workspace-shortcut-switch-7 = ワークスペース7に切り替える
zen-workspace-shortcut-switch-8 = ワークスペース8に切り替える
zen-workspace-shortcut-switch-9 = ワークスペース9に切り替える
zen-workspace-shortcut-switch-10 = ワークスペース10に切り替える
zen-workspace-shortcut-forward = ワークスペースを転送
zen-workspace-shortcut-backward = 後方ワークスペース
zen-sidebar-shortcut-toggle = サイドバーの幅を切り替える
zen-pinned-tab-shortcut-reset = ピン留めされたタブをピン留めしたURLにリセット
zen-split-view-shortcut-grid = 分割表示グリッドの切り替え
zen-split-view-shortcut-vertical = 分割ビューを垂直に切り替える
zen-split-view-shortcut-horizontal = 分割ビューを水平に切り替える
zen-split-view-shortcut-unsplit = 分割ビューを閉じる
zen-key-select-tab-1 = 1つめのタブを選択
zen-key-select-tab-2 = 2つめのタブを選択
zen-key-select-tab-3 = 3つめのタブを選択
zen-key-select-tab-4 = 4つめのタブを選択
zen-key-select-tab-5 = 5つめのタブを選択
zen-key-select-tab-6 = 6つめのタブを選択
zen-key-select-tab-7 = 7つめのタブを選択
zen-key-select-tab-8 = 8つめのタブを選択
zen-key-select-tab-last = 最後のタブを選択
zen-key-show-all-tabs = すべてのタブを表示
zen-key-goto-history = 履歴を開く
zen-key-go-home = ホームに戻る
zen-bookmark-show-sidebar-shortcut = ブックマークサイドバーを表示
zen-bookmark-show-toolbar-shortcut = ブックマークツールバーを表示
zen-devtools-toggle-shortcut = Toggle DevTools
zen-devtools-toggle-browser-toolbox-shortcut = Toggle Browser Toolbox
zen-devtools-toggle-browser-console-shortcut = Toggle Browser Console
zen-devtools-toggle-responsive-design-mode-shortcut = Toggle Responsive Design Mode
zen-devtools-toggle-inspector-shortcut = Toggle Inspector
zen-devtools-toggle-web-console-shortcut = Toggle Web Console
zen-devtools-toggle-js-debugger-shortcut = Toggle JavaScript Debugger
zen-devtools-toggle-net-monitor-shortcut = Toggle Network Monitor
zen-devtools-toggle-style-editor-shortcut = Toggle Style Editor
zen-devtools-toggle-performance-shortcut = Toggle Performance
zen-devtools-toggle-storage-shortcut = Toggle Storage
zen-devtools-toggle-dom-shortcut = Toggle DOM
zen-devtools-toggle-accessibility-shortcut = Toggle Accessibility
