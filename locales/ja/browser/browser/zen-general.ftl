zen-panel-ui-current-profile-text = 使用中のプロファイル
unified-extensions-description = 拡張機能は、 { -brand-short-name } に多くの追加機能をもたらすために使用されます。
tab-context-zen-reset-pinned-tab = 
    .label = ピン留めされたタブをリセット
    .accesskey = R
tab-context-zen-add-essential = 
    .label = Add to Essentials ({ $num } / 12 slots filled)
    .accesskey = E
tab-context-zen-remove-essential = 
    .label = Essentialsから削除
    .accesskey = R
tab-context-zen-replace-pinned-url-with-current = 
    .label = ピン留めされた URL を現在のものに置き換え
    .accesskey = C
zen-themes-corrupted = { -brand-short-name } Modファイルが破損しています。既定のテーマにリセットされました。
zen-shortcuts-corrupted = Your { -brand-short-name } shortcuts file is corrupted. They have been reset to the default shortcuts.
# note: Do not translate the "<br/>" tags in the following string
zen-new-urlbar-notification =
    The new URL bar has been enabled, removing the need for new tab pages.<br/><br/>
    Try opening a new tab to see the new URL bar in action!
zen-disable = 無効
pictureinpicture-minimize-btn = 
    .aria-label = Minimize
    .tooltip = Minimize
zen-panel-ui-gradient-generator-custom-color = カスタムカラー
zen-panel-ui-gradient-generator-saved-message = グラデーションを正常に保存しました！
zen-copy-current-url-confirmation = URLをクリップボードにコピーしました
zen-general-cancel-label = 
    .label = Cancel
zen-general-confirm = 
    .label = Confirm
zen-pinned-tab-replaced = Pinned tab URL has been replaced with the current URL.
zen-tabs-renamed = Tab has been successfully renamed!
zen-background-tab-opened-toast = New background tab opened!
zen-workspace-renamed-toast = Workspace has been successfully renamed!
zen-library-sidebar-workspaces = 
    .label = Spaces
zen-library-sidebar-mods = 
    .label = Mods

# note: Do not translate the "<br/>" tags in the following string

zen-learn-more-text = Learn More
zen-close-label = 閉じる
zen-singletoolbar-urlbar-placeholder-with-name = 
    .placeholder = Search...
zen-icons-picker-emoji = 
    .label = Emojis
zen-icons-picker-svg = 
    .label = Icons
