pane-zen-looks-title = Udseende
category-zen-looks = 
    .tooltiptext = { pane-zen-looks-title }
zen-warning-language = Ændring af standardsproget kan gøre det lettere for hjemmesider at spore dig.
zen-vertical-tabs-layout-header = Browserlayout
zen-vertical-tabs-layout-description = Vælg det layout, der passer dig bedst
zen-layout-single-toolbar = Én værktøjslinje
zen-layout-multiple-toolbar = Flere værktøjslinjer
zen-layout-collapsed-toolbar = Foldet værktøjslinje
sync-currently-syncing-workspaces = Arbejdsområder
sync-engine-workspaces = 
    .label = Arbejdsområder
    .tooltiptext = Synkroniser dine arbejdsområder på tværs af enheder
    .accesskey = W
zen-glance-title = Glimt
zen-glance-header = Generelle indstillinger for glimt
zen-glance-description = Få et hurtigt overblik over dine links uden at åbne dem i en ny fane
zen-glance-trigger-label = Udløsermetode
zen-glance-enabled = 
    .label = Aktiver glimt
zen-glance-trigger-ctrl-click = 
    .label = Ctrl + klik
zen-glance-trigger-alt-click = 
    .label = Alt + klik
zen-glance-trigger-shift-click = 
    .label = Shift + klik
zen-glance-trigger-meta-click = 
    .label = Meta (Kommando) + Klik
zen-glance-trigger-mantain-click = 
    .label = Hold klik (kommer snart!)
zen-look-and-feel-compact-view-header = Vis i kompakt visning
zen-look-and-feel-compact-view-description = Vis kun de værktøjslinjer, du bruger!
zen-look-and-feel-compact-view-enabled = 
    .label = Aktiver { -brand-short-name }'s kompakt tilstand
zen-look-and-feel-compact-view-top-toolbar = 
    .label = Skjul også den øverste værktøjslinje i kompakt tilstand
zen-look-and-feel-compact-toolbar-flash-popup = 
    .label = Vis kortvarigt værktøjslinjen ved skift eller åbning af nye faner i kompakt tilstand
pane-zen-tabs-title = Fanehåndtering
category-zen-workspaces = 
    .tooltiptext = { pane-zen-tabs-title }
pane-settings-workspaces-title = Arbejdsområder
zen-tabs-unloader-enabled = 
    .label = Aktiver fanedeaktivering
zen-look-and-feel-compact-toolbar-themed = 
    .label = Brug temabaggrund til kompakt værktøjslinje
zen-look-and-feel-compact-sidebar-themed = 
    .label = Brug temabaggrund til kompakt sidepanel
zen-workspace-continue-where-left-off = 
    .label = Fortsæt, hvor du slap
pane-zen-pinned-tab-manager-title = Fastgjorte Faner
zen-pinned-tab-manager-header = Generelle indstillinger for fastgjorte faner
zen-pinned-tab-manager-description = Administrer yderligere adfærd for fastgjorte faner
zen-pinned-tab-manager-restore-pinned-tabs-to-pinned-url = 
    .label = Gendan fastgjorte faner til deres oprindeligt fastgjorte URL ved opstart
zen-pinned-tab-manager-container-specific-essentials-enabled = 
    .label = Aktiver beholderspecifikke essentielle
zen-pinned-tab-manager-close-shortcut-behavior-label = Adfærd for Genvejen Luk Fane
zen-pinned-tab-manager-reset-unload-switch-close-shortcut-option = 
    .label = Nulstil URL, fjern og skift til næste fane
zen-pinned-tab-manager-unload-switch-close-shortcut-option = 
    .label = Fjern og skift til næste fane
zen-pinned-tab-manager-reset-switch-close-shortcut-option = 
    .label = Nulstil URL og skift til næste fane
zen-pinned-tab-manager-switch-close-shortcut-option = 
    .label = Skift til næste fane
zen-pinned-tab-manager-reset-close-shortcut-option = 
    .label = Nulstil URL
zen-pinned-tab-manager-close-close-shortcut-option = 
    .label = Luk fane
pane-zen-workspaces-header = Arbejdsområder
zen-settings-workspaces-header = Generelle indstillinger for arbejdsområder
zen-settings-workspaces-description = Med arbejdsområder kan du have flere browsing-sessioner på én gang!
zen-settings-workspaces-enabled = 
    .label = Aktiver arbejdsområder
zen-settings-workspaces-hide-default-container-indicator = 
    .label = Skjul standardindikatoren for beholdere i fanelinjen
zen-key-unsaved = Ikke-gemt genvej! Gem den ved at klikke på "Escape"-tasten efter at have indtastet den igen.
zen-key-conflict = Konflikt med en anden genvej
pane-zen-theme-title = Temaindstillinger
zen-vertical-tabs-title = Layout for sidepanel og faner
zen-vertical-tabs-header = Lodrette Faner
zen-vertical-tabs-description = Håndter dine faner i et lodret layout
zen-vertical-tabs-show-expand-button = 
    .label = Vis knappen Udvid
zen-vertical-tabs-newtab-on-tab-list = 
    .label = Vis knappen Ny Fane på fanelisten
zen-vertical-tabs-newtab-top-button-up = 
    .label = Flyt knappen for ny fane øverst
zen-vertical-tabs-expand-tabs-by-default = Udvid faner som standard
zen-vertical-tabs-dont-expand-tabs-by-default = Udvid ikke faner som standard
zen-vertical-tabs-expand-tabs-on-hover = Udvid faner ved at holde musen over (virker ikke i kompakt tilstand)
zen-vertical-tabs-expand-tabs-header = Sådan udvider du faneblade
zen-vertical-tabs-expand-tabs-description = Vælg hvordan du udvider faner i sidepanelet
zen-theme-marketplace-header = Zen-temaer
zen-theme-disable-all-enabled = 
    .title = Deaktivér alle temaer
zen-theme-disable-all-disabled = 
    .title = Aktivér alle temaer
zen-theme-marketplace-description = Find og installer temaer fra butikken.
zen-theme-marketplace-remove-button = 
    .label = Fjern Tema
zen-theme-marketplace-check-for-updates-button = 
    .label = Søg efter opdateringer
zen-theme-marketplace-import-button = 
    .label = Importér mods
zen-theme-marketplace-export-button = 
    .label = Eksportér mods
zen-theme-marketplace-import-success = Mods blev importeret
zen-theme-marketplace-import-failure = Der opstod en fejl ved import af mods
zen-theme-marketplace-export-success = Mods blev eksporteret
zen-theme-marketplace-export-failure = Der opstod en fejl ved eksport af mods
zen-theme-marketplace-updates-success = Tema opdateret
zen-theme-marketplace-updates-failure = Kunne ikke finde nogen opdateringer!
zen-theme-marketplace-toggle-enabled-button = 
    .title = Deaktivér Tema
zen-theme-marketplace-toggle-disabled-button = 
    .title = Aktivér Tema
zen-theme-marketplace-remove-confirmation = Er du sikker på, at du vil fjerne denne mod?
zen-theme-marketplace-close-modal = Luk
zen-theme-marketplace-theme-header-title = 
    .title = CSS-vælger: { $name }
zen-theme-marketplace-dropdown-default-label = 
    .label = Ingen
zen-theme-marketplace-input-default-placeholder = 
    .placeholder = Skriv noget...
pane-zen-marketplace-title = Zen Mods
zen-themes-auto-update = 
    .label = Opdater automatisk installerede mods ved opstart
zen-settings-workspaces-force-container-tabs-to-workspace = 
    .label = Skift til et arbejdsområde, hvor beholderen er indstillet som standard, når du åbner beholderfaner
zen-theme-marketplace-link = Besøg Butik
zen-dark-theme-styles-header = Mørke Tema Stilarter
zen-dark-theme-styles-description = Tilpas det mørke tema til din smag
zen-dark-theme-styles-amoled = Nat Tema
zen-dark-theme-styles-default = Standard Mørkt Tema
zen-dark-theme-styles-colorful = Farverigt Mørkt Tema
zen-compact-mode-styles-left = Skjul fanelinje
zen-compact-mode-styles-top = Skjul øverste bjælke
zen-compact-mode-styles-both = Skjul Begge
zen-urlbar-title = Zen URL-linje
zen-urlbar-header = Generelle indstillinger for URL-linjen
zen-urlbar-description = Tilpas URL-linjen til din smag
zen-urlbar-behavior-label = Adfærd
zen-urlbar-behavior-normal = 
    .label = Normal
zen-urlbar-behavior-floating-on-type = 
    .label = Flydende kun ved skrivning
zen-urlbar-behavior-float = 
    .label = Altid flydende
pane-zen-CKS-title = Tastaturgenveje
category-zen-CKS = 
    .tooltiptext = { pane-zen-CKS-title }
pane-settings-CKS-title = { -brand-short-name } Tastaturgenveje
zen-settings-CKS-header = Tilpas dine tastaturgenveje
zen-settings-CKS-description = Skift standardtastaturgenveje efter din smag, og forbedr din browseroplevelse
zen-settings-CKS-disable-firefox = 
    .label = Deaktivér { -brand-short-name }'s standardtastaturgenveje
zen-settings-CKS-duplicate-shortcut = 
    .label = Dupliker genvej
zen-settings-CKS-reset-shortcuts = 
    .label = Nulstil til standard
zenCKSOption-group-other = Andet
zenCKSOption-group-windowAndTabManagement = Vindue- & Fanehåndtering
zenCKSOption-group-navigation = Navigation
zenCKSOption-group-searchAndFind = Søg & Find
zenCKSOption-group-pageOperations = Sidehandlinger
zenCKSOption-group-historyAndBookmarks = Historik & Bogmærker
zenCKSOption-group-mediaAndDisplay = Medie & Visning
zenCKSOption-group-zen-compact-mode = Kompakt Tilstand
zenCKSOption-group-zen-workspace = Arbejdsområder
zenCKSOption-group-zen-other = Andre Zen-funktioner
zenCKSOption-group-zen-split-view = Delt Visning
zenCKSOption-group-devTools = Udviklerværktøjer
zen-key-quick-restart = Hurtig Genstart
zen-window-new-shortcut = Nyt Vindue
zen-tab-new-shortcut = Ny Fane
zen-key-redo = Gendan
zen-restore-last-closed-tab-shortcut = Gendan Sidst Lukket Fane
zen-location-open-shortcut = Åbn Placering
zen-location-open-shortcut-alt = Åbn Placering (Alt)
zen-key-undo-close-window = Fortryd Lukning af Vindue
zen-text-action-undo-shortcut = Fortryd
zen-text-action-redo-shortcut = Gendan
zen-text-action-cut-shortcut = Klip
zen-text-action-copy-shortcut = Kopiér
zen-text-action-copy-url-shortcut = Kopiér nuværende URL
zen-text-action-copy-url-markdown-shortcut = Kopiér nuværende URL som Markdown
zen-text-action-paste-shortcut = Indsæt
zen-text-action-select-all-shortcut = Vælg Alle
zen-text-action-delete-shortcut = Slet
zen-history-show-all-shortcut-mac = Vis Al Historik (Mac)
zen-full-screen-shortcut = Slå Fuld Skærm Til/Fra
zen-reader-mode-toggle-shortcut-windows = Slå Læsertilstand Til/Fra (Windows)
zen-picture-in-picture-toggle-shortcut-alt = Slå Billede-i-Billede Til/Fra (Alt)
zen-picture-in-picture-toggle-shortcut-mac = Slå Billede-i-Billede Til/Fra (Mac)
zen-picture-in-picture-toggle-shortcut-mac-alt = Slå Billede-i-Billede Til/Fra (Mac Alt)
zen-page-source-shortcut-safari = Vis Sidekilde (Safari)
zen-nav-stop-shortcut = Stop Indlæsning
zen-history-sidebar-shortcut = Vis sidepanel for historik
zen-window-minimize-shortcut = Minimer Vindue
zen-help-shortcut = Åbn Hjælp
zen-preferences-shortcut = Åbn Indstillinger
zen-hide-app-shortcut = Skjul Applikation
zen-hide-other-apps-shortcut = Skjul Andre Applikationer
zen-search-focus-shortcut = Fokuseret Søgning
zen-search-focus-shortcut-alt = Fokuseret Søgning (Alt)
zen-downloads-shortcut = Open Downloads
zen-addons-shortcut = Åbn tilføjelser
zen-file-open-shortcut = Åbn fil
zen-save-page-shortcut = Gem side
zen-print-shortcut = Udskriv side
zen-close-shortcut-2 = Luk fane
zen-mute-toggle-shortcut = Slå Lyden Til/Fra
zen-key-delete = Slet nøgle
zen-key-go-back = Gå tilbage
zen-key-go-forward = Gå frem
zen-nav-back-shortcut-alt = Naviger tilbage (Alt)
zen-nav-fwd-shortcut-alt = Naviger frem (Alt)
zen-history-show-all-shortcut = Vis al historik
zen-key-enter-full-screen = Gå Ind i Fuld Skærm
zen-key-exit-full-screen = Afslut fuld skærm
zen-ai-chatbot-sidebar-shortcut = Slå sidepanel for AI-chatbot til/fra
zen-key-inspector-mac = Slå inspektør til/fra (Mac)
zen-toggle-sidebar-shortcut = Slå Firefox-sidepanel til/fra
zen-reader-mode-toggle-shortcut-other = Slå Læsertilstand Til/Fra
zen-picture-in-picture-toggle-shortcut = Slå Billede-i-Billede Til/Fra
zen-nav-reload-shortcut-2 = Genindlæs side
zen-key-about-processes = Om processer
zen-page-source-shortcut = Vis sidekilde
zen-page-info-shortcut = Se sideoplysninger
zen-find-shortcut = Find på side
zen-search-find-again-shortcut = Find igen
zen-search-find-again-shortcut-prev = Find forrige
zen-search-find-again-shortcut-2 = Find igen (Alt)
zen-bookmark-this-page-shortcut = Bogmærk denne side
zen-bookmark-show-library-shortcut = Vis Bogmærkebibliotek
zen-key-stop = Stop indlæsning
zen-full-zoom-reduce-shortcut = Zoom ud
zen-full-zoom-enlarge-shortcut = Zoom ind
zen-full-zoom-reset-shortcut = Nulstil zoom
zen-full-zoom-reset-shortcut-alt = Nulstil zoom (Alt)
zen-full-zoom-enlarge-shortcut-alt = Zoom ind (Alt)
zen-full-zoom-enlarge-shortcut-alt2 = Zoom ind (Alt 2)
zen-bidi-switch-direction-shortcut = Skift tekstretning
zen-private-browsing-shortcut = Privat browsing
zen-screenshot-shortcut = Tag skærmbillede
zen-key-sanitize = Ryd browserdata
zen-quit-app-shortcut = Afslut applikation
zen-key-wr-capture-cmd = WR-optagelseskommando
zen-key-wr-toggle-capture-sequence-cmd = Slå WR-optagelsessekvens Til/Fra
zen-nav-reload-shortcut = Genindlæs side
zen-nav-reload-shortcut-skip-cache = Genindlæs Side (Spring Cache Over)
zen-close-shortcut = Luk vindue
zen-close-tab-shortcut = Luk fane
zen-compact-mode-shortcut-show-sidebar = Flydende sidepanel til/fra
zen-compact-mode-shortcut-show-toolbar = Flydende værktøjslinje til/fra
zen-compact-mode-shortcut-toggle = Toggle Compact Mode
zen-workspace-shortcut-switch-1 = Skift til arbejdsområde 1
zen-workspace-shortcut-switch-2 = Skift til arbejdsområde 2
zen-workspace-shortcut-switch-3 = Skift til arbejdsområde 3
zen-workspace-shortcut-switch-4 = Skift til arbejdsområde 4
zen-workspace-shortcut-switch-5 = Skift til arbejdsområde 5
zen-workspace-shortcut-switch-6 = Skift til arbejdsområde 6
zen-workspace-shortcut-switch-7 = Skift til arbejdsområde 7
zen-workspace-shortcut-switch-8 = Skift til arbejdsområde 8
zen-workspace-shortcut-switch-9 = Skift til arbejdsområde 9
zen-workspace-shortcut-switch-10 = Skift til arbejdsområde 10
zen-workspace-shortcut-forward = Fremad Arbejdsområde
zen-workspace-shortcut-backward = Bagudrettet Arbejdsrum
zen-sidebar-shortcut-toggle = Skift sidepanelets bredde
zen-pinned-tab-shortcut-reset = Nulstil fastgjort fane til fastgjort URL
zen-split-view-shortcut-grid = Opdelt visning i gitter til/fra
zen-split-view-shortcut-vertical = Lodret opdelt visning til/fra
zen-split-view-shortcut-horizontal = Vandret opdelt visning til/fra
zen-split-view-shortcut-unsplit = Luk opdelt visning
zen-key-select-tab-1 = Vælg fane #1
zen-key-select-tab-2 = Vælg fane #2
zen-key-select-tab-3 = Vælg fane #3
zen-key-select-tab-4 = Vælg fane #4
zen-key-select-tab-5 = Vælg fane #5
zen-key-select-tab-6 = Vælg fane #6
zen-key-select-tab-7 = Vælg fane #7
zen-key-select-tab-8 = Vælg fane #8
zen-key-select-tab-last = Vælg sidste fane
zen-key-show-all-tabs = Vis alle faner
zen-key-goto-history = Gå til historik
zen-key-go-home = Gå hjem
zen-bookmark-show-sidebar-shortcut = Vis Bogmærker-sidepanel
zen-bookmark-show-toolbar-shortcut = Vis bogmærkelinje
zen-devtools-toggle-shortcut = Vis/skjul Udviklerværktøjer
zen-devtools-toggle-browser-toolbox-shortcut = Vis/skjul Browserværktøjskasse
zen-devtools-toggle-browser-console-shortcut = Vis/skjul Browserkonsol
zen-devtools-toggle-responsive-design-mode-shortcut = Responsiv designtilstand til/fra
zen-devtools-toggle-inspector-shortcut = Vis/skjul Inspektør
zen-devtools-toggle-web-console-shortcut = Vis/skjul Webkonsol
zen-devtools-toggle-js-debugger-shortcut = Vis/skjul JavaScript-fejlfinding
zen-devtools-toggle-net-monitor-shortcut = Vis/skjul Netværksmonitor
zen-devtools-toggle-style-editor-shortcut = Vis/skjul Stileditor
zen-devtools-toggle-performance-shortcut = Vis/skjul Ydeevne
zen-devtools-toggle-storage-shortcut = Vis/skjul Lager
zen-devtools-toggle-dom-shortcut = Vis/skjul DOM
zen-devtools-toggle-accessibility-shortcut = Vis/skjul Tilgængelighed
