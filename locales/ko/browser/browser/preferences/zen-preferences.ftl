pane-zen-looks-title = 모양
category-zen-looks = 
    .tooltiptext = { pane-zen-looks-title }
zen-warning-language = 기본 언어를 변경하면 웹사이트에서 사용자를 추적하기 더 쉬워질 수 있습니다.
zen-vertical-tabs-layout-header = 브라우저 레이아웃
zen-vertical-tabs-layout-description = 가장 잘 맞는 레이아웃을 고르세요
zen-layout-single-toolbar = 단일 툴바
zen-layout-multiple-toolbar = 다중 툴바
zen-layout-collapsed-toolbar = 축소된 툴바
sync-currently-syncing-workspaces = 워크스페이스
sync-engine-workspaces = 
    .label = 워크스페이스
    .tooltiptext = 여러 기기의 워크스페이스 동기화
    .accesskey = W
zen-glance-title = 글랜스
zen-glance-header = 글랜스 기본 설정
zen-glance-description = 새로운 탭을 열지 않고도 한눈에 링크를 미리 볼 수 있습니다
zen-glance-trigger-label = 트리거 방식
zen-glance-enabled = 
    .label = 글랜스 활성화
zen-glance-trigger-ctrl-click = 
    .label = Ctrl + 클릭
zen-glance-trigger-alt-click = 
    .label = Alt + 클릭
zen-glance-trigger-shift-click = 
    .label = Shift + 클릭
zen-glance-trigger-meta-click = 
    .label = Meta (Command) + 클릭
zen-glance-trigger-mantain-click = 
    .label = 길게 클릭 (Coming Soon!)
zen-look-and-feel-compact-view-header = 사이드바 축소 모드로 보기
zen-look-and-feel-compact-view-description = 사용하는 툴바만 보세요!
zen-look-and-feel-compact-view-enabled = 
    .label = { -brand-short-name }의 사이드바 축소 모드 활성화
zen-look-and-feel-compact-view-top-toolbar = 
    .label = 사이드바 축소 모드에서 상단 툴바 숨기기
zen-look-and-feel-compact-toolbar-flash-popup = 
    .label = 사이드바 축소 모드에서 새 탭을 전환하거나 열 때 툴바를 간략하게 팝업으로 표시합니다
pane-zen-tabs-title = 탭 관리
category-zen-workspaces = 
    .tooltiptext = { pane-zen-tabs-title }
pane-settings-workspaces-title = 워크스페이스
zen-tabs-unloader-enabled = 
    .label = 탭 언로더 활성화
zen-look-and-feel-compact-toolbar-themed = 
    .label = 축소된 툴바에 테마 배경 사용
zen-look-and-feel-compact-sidebar-themed = 
    .label = 축소된 사이드바에 테마 배경 사용
zen-workspace-continue-where-left-off = 
    .label = 종료했던 탭에서 다시 시작하기
pane-zen-pinned-tab-manager-title = 고정된 탭
zen-pinned-tab-manager-header = 고정된 탭 설정
zen-pinned-tab-manager-description = 고정된 탭만의 추가적인 기능을 설정하세요
zen-pinned-tab-manager-restore-pinned-tabs-to-pinned-url = 
    .label = 브라우저가 열릴 때 고정된 탭의 URL 초기화
zen-pinned-tab-manager-container-specific-essentials-enabled = 
    .label = 컨테이너마다 다른 에센셜 사용 활성화
zen-pinned-tab-manager-close-shortcut-behavior-label = 탭 닫기 단축키의 행동
zen-pinned-tab-manager-reset-unload-switch-close-shortcut-option = 
    .label = URL 초기화, 탭 언로드 후 다음 탭으로 이동
zen-pinned-tab-manager-unload-switch-close-shortcut-option = 
    .label = 탭 언로드 후 다음 탭으로 이동
zen-pinned-tab-manager-reset-switch-close-shortcut-option = 
    .label = URL 초기화 후 다음 탭으로 이동
zen-pinned-tab-manager-switch-close-shortcut-option = 
    .label = 다음 탭으로 이동
zen-pinned-tab-manager-reset-close-shortcut-option = 
    .label = URL 초기화
zen-pinned-tab-manager-close-close-shortcut-option = 
    .label = 탭 닫기
pane-zen-workspaces-header = 워크스페이스
zen-settings-workspaces-header = 워크스페이스 기본 설정
zen-settings-workspaces-description = 워크스페이스를 사용해 여러 개의 브라우저 세션을 동시에 사용할 수 있습니다!
zen-settings-workspaces-enabled = 
    .label = 워크스페이스 활성화
zen-settings-workspaces-hide-default-container-indicator = 
    .label = 탭에서 기본 컨테이너 표시 숨기기
zen-key-unsaved = 단축키가 저장되지 않았습니다! 다시 입력한 후 "Escape" 키를 눌러 저장하세요.
zen-key-conflict = 다른 단축키와 겹침
pane-zen-theme-title = 테마 설정
zen-vertical-tabs-title = 사이드바 및 탭 레이아웃
zen-vertical-tabs-header = 수직 탭
zen-vertical-tabs-description = 수직 레이아웃에서 탭을 관리하세요
zen-vertical-tabs-show-expand-button = 
    .label = 확장 버튼 보이기
zen-vertical-tabs-newtab-on-tab-list = 
    .label = 탭 목록에 새 탭 버튼 표시하기
zen-vertical-tabs-newtab-top-button-up = 
    .label = 새 탭 버튼을 위에 표시하기
zen-vertical-tabs-expand-tabs-by-default = 기본값으로 탭을 확장하기
zen-vertical-tabs-dont-expand-tabs-by-default = 기본값으로 탭을 확장하지 않기
zen-vertical-tabs-expand-tabs-on-hover = 커서를 올려 탭 확장 (사이드바 축소 모드에서는 작동하지 않음)
zen-vertical-tabs-expand-tabs-header = 탭 확장 방식
zen-vertical-tabs-expand-tabs-description = 사이드바 탭이 어떻게 확장될지 선택하세요
zen-theme-marketplace-header = Zen 모드
zen-theme-disable-all-enabled = 
    .title = 모든 모드 비활성화
zen-theme-disable-all-disabled = 
    .title = 모든 모드 활성화
zen-theme-marketplace-description = 스토어에서 모드를 찾고 설치하세요.
zen-theme-marketplace-remove-button = 
    .label = 모드 제거
zen-theme-marketplace-check-for-updates-button = 
    .label = 업데이트 확인
zen-theme-marketplace-import-button = 
    .label = 모드 불러오기
zen-theme-marketplace-export-button = 
    .label = 모드 내보내기
zen-theme-marketplace-import-success = 모드를 성공적으로 불러왔습니다
zen-theme-marketplace-import-failure = 모드를 불러오는 중 오류가 발생했습니다
zen-theme-marketplace-export-success = 모드를 성공적으로 내보냈습니다
zen-theme-marketplace-export-failure = 모드를 내보내는 중 오류가 발생했습니다
zen-theme-marketplace-updates-success = 모드가 업데이트 되었습니다
zen-theme-marketplace-updates-failure = 업데이트를 찾을 수 없습니다!
zen-theme-marketplace-toggle-enabled-button = 
    .title = 모드 비활성화
zen-theme-marketplace-toggle-disabled-button = 
    .title = 모드 활성화
zen-theme-marketplace-remove-confirmation = 정말 이 모드를 제거하시겠습니까?
zen-theme-marketplace-close-modal = 닫기
zen-theme-marketplace-theme-header-title = 
    .title = CSS 선택자: { $name }
zen-theme-marketplace-dropdown-default-label = 
    .label = 없음
zen-theme-marketplace-input-default-placeholder = 
    .placeholder = 입력...
pane-zen-marketplace-title = Zen 모드
zen-themes-auto-update = 
    .label = 시작 시 설치된 모드 자동 업데이트
zen-settings-workspaces-force-container-tabs-to-workspace = 
    .label = 컨테이너 탭을 열 때 해당 컨테이너가 기본값으로 설정된 워크스페이스로 전환하기
zen-theme-marketplace-link = 스토어 방문
zen-dark-theme-styles-header = 어두운 테마 스타일
zen-dark-theme-styles-description = 어두운 테마를 취향대로 커스터마이징하세요
zen-dark-theme-styles-amoled = 밤 테마
zen-dark-theme-styles-default = 기본 어두운 테마
zen-dark-theme-styles-colorful = 화려한 어두운 테마
zen-compact-mode-styles-left = 탭 바 숨기기
zen-compact-mode-styles-top = 상단 바 숨기기
zen-compact-mode-styles-both = 둘 다 숨기기
zen-urlbar-title = Zen 주소 표시줄
zen-urlbar-header = 주소 표시줄 일반 설정
zen-urlbar-description = 주소 표시줄을 취향껏 커스터마이징하세요
zen-urlbar-behavior-label = 작동
zen-urlbar-behavior-normal = 
    .label = 일반
zen-urlbar-behavior-floating-on-type = 
    .label = 입력할 때만 뜨게 하기
zen-urlbar-behavior-float = 
    .label = 항상 뜨게 하기
pane-zen-CKS-title = 키보드 단축키
category-zen-CKS = 
    .tooltiptext = { pane-zen-CKS-title }
pane-settings-CKS-title = { -brand-short-name } 키보드 단축키
zen-settings-CKS-header = 단축키를 커스터마이징하세요
zen-settings-CKS-description = 기본 키보드 단축키를 입맛대로 바꾸고 브라우징 경험을 개선하세요
zen-settings-CKS-disable-firefox = 
    .label = { -brand-short-name }의 기본 키보드 단축키 비활성화
zen-settings-CKS-duplicate-shortcut = 
    .label = 단축키 복제
zen-settings-CKS-reset-shortcuts = 
    .label = 기본값으로 재설정
zenCKSOption-group-other = 기타
zenCKSOption-group-windowAndTabManagement = 창 & 탭 관리
zenCKSOption-group-navigation = 이동
zenCKSOption-group-searchAndFind = 검색 & 찾기
zenCKSOption-group-pageOperations = 페이지 작동
zenCKSOption-group-historyAndBookmarks = 방문기록 & 북마크
zenCKSOption-group-mediaAndDisplay = 미디어 & 화면
zenCKSOption-group-zen-compact-mode = 사이드바 축소 모드
zenCKSOption-group-zen-workspace = 워크스페이스
zenCKSOption-group-zen-other = 다른 Zen 기능들
zenCKSOption-group-zen-split-view = 나눠서 보기
zenCKSOption-group-devTools = 개발자 도구
zen-key-quick-restart = 빠른 재시작
zen-window-new-shortcut = 새 창
zen-tab-new-shortcut = 새 탭
zen-key-redo = 취소 번복
zen-restore-last-closed-tab-shortcut = 마지막으로 닫힌 탭 복구
zen-location-open-shortcut = 위치 열기
zen-location-open-shortcut-alt = 위치 열기 (Alt)
zen-key-undo-close-window = 창 닫기 취소
zen-text-action-undo-shortcut = 되돌리기
zen-text-action-redo-shortcut = 되돌리기 취소
zen-text-action-cut-shortcut = 자르기
zen-text-action-copy-shortcut = 복사
zen-text-action-copy-url-shortcut = 현재 URL 복사
zen-text-action-copy-url-markdown-shortcut = 마크다운 형식으로 현재 URL 복사하기
zen-text-action-paste-shortcut = 붙여넣기
zen-text-action-select-all-shortcut = 모두 선택
zen-text-action-delete-shortcut = 삭제
zen-history-show-all-shortcut-mac = 모든 기록 보기 (Mac)
zen-full-screen-shortcut = 전체화면 켜기/끄기
zen-reader-mode-toggle-shortcut-windows = 읽기 모드 켜기/끄기 (Windows)
zen-picture-in-picture-toggle-shortcut-alt = Picture-in-Picture 켜기/끄기 (Alt)
zen-picture-in-picture-toggle-shortcut-mac = Picture-in-Picture 켜기/끄기 (Mac)
zen-picture-in-picture-toggle-shortcut-mac-alt = Picture-in-Picture 켜기/끄기 (Mac Alt)
zen-page-source-shortcut-safari = 페이지 소스 보기 (Safari)
zen-nav-stop-shortcut = 로딩 멈추기
zen-history-sidebar-shortcut = 방문 기록 사이드바 열기
zen-window-minimize-shortcut = 창 최소화
zen-help-shortcut = 도움말 열기
zen-preferences-shortcut = 설정 열기
zen-hide-app-shortcut = 애플리케이션 숨기기
zen-hide-other-apps-shortcut = 다른 애플리케이션 숨기기
zen-search-focus-shortcut = 검색 열기
zen-search-focus-shortcut-alt = 검색 열기 (Alt)
zen-downloads-shortcut = 다운로드 열기
zen-addons-shortcut = 애드온 열기
zen-file-open-shortcut = 파일 열기
zen-save-page-shortcut = 페이지 저장
zen-print-shortcut = 페이지 출력
zen-close-shortcut-2 = 탭 닫기
zen-mute-toggle-shortcut = 음소거 켜기/끄기
zen-key-delete = 삭제 키
zen-key-go-back = 뒤로 가기
zen-key-go-forward = 앞으로 가기
zen-nav-back-shortcut-alt = 뒤로 이동 (Alt)
zen-nav-fwd-shortcut-alt = 앞으로 이동 (Alt)
zen-history-show-all-shortcut = 모든 기록 보기
zen-key-enter-full-screen = 전체화면 활성화
zen-key-exit-full-screen = 전체화면 비활성화
zen-ai-chatbot-sidebar-shortcut = AI 챗봇 사이드바 켜기/끄기
zen-key-inspector-mac = 검사기(Inspector) 전환
zen-toggle-sidebar-shortcut = 파이어폭스 사이드바 켜기/끄기
zen-reader-mode-toggle-shortcut-other = 읽기 모드 켜기/끄기
zen-picture-in-picture-toggle-shortcut = Picture-in-Picture 켜기/끄기
zen-nav-reload-shortcut-2 = 페이지 새로고침
zen-key-about-processes = 프로세스 관련
zen-page-source-shortcut = 페이지 소스 보기
zen-page-info-shortcut = 페이지 정보 보기
zen-find-shortcut = 페이지에서 검색
zen-search-find-again-shortcut = 재검색
zen-search-find-again-shortcut-prev = 뒤로 검색
zen-search-find-again-shortcut-2 = 재검색 (Alt)
zen-bookmark-this-page-shortcut = 이 페이지 북마크
zen-bookmark-show-library-shortcut = 북마크 라이브러리 보기
zen-key-stop = 로딩 멈추기
zen-full-zoom-reduce-shortcut = 축소
zen-full-zoom-enlarge-shortcut = 확대
zen-full-zoom-reset-shortcut = 확대 초기화
zen-full-zoom-reset-shortcut-alt = 확대 초기화 (Alt)
zen-full-zoom-enlarge-shortcut-alt = 확대 (Alt)
zen-full-zoom-enlarge-shortcut-alt2 = 확대 (Alt 2)
zen-bidi-switch-direction-shortcut = 글 방향 전환
zen-private-browsing-shortcut = 프라이빗 브라우징
zen-screenshot-shortcut = 스크린샷 찍기
zen-key-sanitize = 브라우징 데이터 삭제
zen-quit-app-shortcut = 애플리케이션 종료
zen-key-wr-capture-cmd = WR 캡쳐 명령
zen-key-wr-toggle-capture-sequence-cmd = WR 캡쳐 시퀀스 켜기/끄기
zen-nav-reload-shortcut = 페이지 새로고침
zen-nav-reload-shortcut-skip-cache = 페이지 새로고침 (캐시 무시)
zen-close-shortcut = 창 닫기
zen-close-tab-shortcut = 탭 닫기
zen-compact-mode-shortcut-show-sidebar = 떠 있는 사이드바 켜기/끄기
zen-compact-mode-shortcut-show-toolbar = 떠 있는 툴바 켜기/끄기
zen-compact-mode-shortcut-toggle = 사이드바 축소 모드 켜기/끄기
zen-workspace-shortcut-switch-1 = 워크스페이스 1로 전환
zen-workspace-shortcut-switch-2 = 워크스페이스 2로 전환
zen-workspace-shortcut-switch-3 = 워크스페이스 3으로 전환
zen-workspace-shortcut-switch-4 = 워크스페이스 4로 전환
zen-workspace-shortcut-switch-5 = 워크스페이스 5로 전환
zen-workspace-shortcut-switch-6 = 워크스페이스 6으로 전환
zen-workspace-shortcut-switch-7 = 워크스페이스 7로 전환
zen-workspace-shortcut-switch-8 = 워크스페이스 8로 전환
zen-workspace-shortcut-switch-9 = 워크스페이스 9로 전환
zen-workspace-shortcut-switch-10 = 워크스페이스 10으로 전환
zen-workspace-shortcut-forward = 다음 워크스페이스로
zen-workspace-shortcut-backward = 이전 워크스페이스로
zen-sidebar-shortcut-toggle = 사이드바 넓이 켜기/끄기
zen-pinned-tab-shortcut-reset = 고정된 탭 URL 초기화
zen-split-view-shortcut-grid = 그리드 스플릿 뷰 전환
zen-split-view-shortcut-vertical = 수직 스플릿 뷰 전환
zen-split-view-shortcut-horizontal = 수평 스플릿 뷰 전환
zen-split-view-shortcut-unsplit = 스플릿 뷰 닫기
zen-key-select-tab-1 = 탭 #1 선택
zen-key-select-tab-2 = 탭 #2 선택
zen-key-select-tab-3 = 탭 #3 선택
zen-key-select-tab-4 = 탭 #4 선택
zen-key-select-tab-5 = 탭 #5 선택
zen-key-select-tab-6 = 탭 #6 선택
zen-key-select-tab-7 = 탭 #7 선택
zen-key-select-tab-8 = 탭 #8 선택
zen-key-select-tab-last = 마지막 탭 선택
zen-key-show-all-tabs = 모든 탭 보기
zen-key-goto-history = 방문 기록 열기
zen-key-go-home = 홈으로 이동
zen-bookmark-show-sidebar-shortcut = 북마크 사이드바 보기
zen-bookmark-show-toolbar-shortcut = 북마크 툴바 보기
zen-devtools-toggle-shortcut = 개발자 도구 전환
zen-devtools-toggle-browser-toolbox-shortcut = 브라우저 개발자 도구 전환
zen-devtools-toggle-browser-console-shortcut = 브라우저 콘솔 전환
zen-devtools-toggle-responsive-design-mode-shortcut = 반응형 디자인 모드 전환
zen-devtools-toggle-inspector-shortcut = 검사기(Inspector) 전환
zen-devtools-toggle-web-console-shortcut = 웹 콘솔 전환
zen-devtools-toggle-js-debugger-shortcut = 자바스크립트 디버거 전환
zen-devtools-toggle-net-monitor-shortcut = 네트워크 모니터 전환
zen-devtools-toggle-style-editor-shortcut = 스타일 편집기 전환
zen-devtools-toggle-performance-shortcut = 성능 탭 전환
zen-devtools-toggle-storage-shortcut = 저장소 탭 전환
zen-devtools-toggle-dom-shortcut = DOM 탭 전환
zen-devtools-toggle-accessibility-shortcut = 접근성 탭 전환
